<template>
  <vxe-grid
    class="bg-card ml-2 flex-1 rounded-lg border p-2"
    v-bind="detailListOptions"
    ref="detailListRef"
    @cell-click="handDetailListCellClick"
    @edit-closed="handleRightEditClose"
  >
    <template #typeSlot="{ row }">
      {{ materialDict.find((item) => item.value === row.type)?.label }}
    </template>
    <!--  业务成本科目名称 -->
    <template #businessCost="{ row }">
      <ElTreeSelect
        class="treeSelect-box"
        multiple
        collapse-tags
        :data="businessCostAccountTreeList"
        v-model="row.businessCostSubjectDetailIds"
        show-checkbox
        check-strictly
        default-expand-all
        node-key="id"
        :props="{
          disabled: (data: any) => !data.isLeaf,
          label: 'name',
          children: 'children',
        }"
        filterable
        :teleported="false"
        :filter-node-method="filterNodeMethod"
      />
    </template>
    <template #businessCost_default="{ row }">
      <span>
        {{ filterBusinessCostAccountLabel(row.businessCostSubjectDetailIds) }}
      </span>
    </template>
  </vxe-grid>
</template>

<script setup lang="ts">
import { defineEmits, defineProps, onMounted, reactive, ref, watch } from 'vue';

import { ElMessage, ElMessageBox, ElTreeSelect } from 'element-plus';

import { TreeBusinessCostCategoryDetails } from '#/api/enterpriseCenter/enterpriseStandards/businessCostSubject';
import { UpdateCostDicDetail } from '#/api/enterpriseCenter/enterpriseStandards/costDictRelease';
import {
  DeleteSelectDetailData,
  SelectDetailsList,
} from '#/api/projectCenter/projectSetting/businessCost';
import { getNamesFromTreeByIds } from '#/utils/common';

import { materialDict } from '../../../data';

const props = withDefaults(
  defineProps<{
    categoryId: string;
    dictTypeId: string;
    quoteId: string;
    versionId: string;
  }>(),
  {
    dictTypeId: '',
    categoryId: '',
    versionId: '',
    quoteId: '',
  },
);

const emit = defineEmits<{
  (e: 'detailRefresh'): void;
}>();

// 字典明细
const detailListRef = ref();
const detailListOptions = reactive({
  height: '100%',
  size: 'mini',
  border: true,
  keepSource: true,
  align: 'center',
  showOverflow: true,
  columnConfig: {
    resizable: true,
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    showIcon: false,
  },
  rowConfig: {
    isCurrent: true,
  },
  columns: [
    {
      width: 100,
      title: '编码',
      field: 'code',
    },
    {
      width: 120,
      field: 'name',
      title: '名称',
    },
    {
      width: 120,
      field: 'specificationModel',
      title: '规格型号',
    },
    {
      width: 120,
      field: 'remark',
      title: '备注',
    },
    {
      field: 'businessCostSubjectDetailIds',
      title: '业务成本科目名称',
      editRender: {},
      slots: {
        edit: 'businessCost',
        default: 'businessCost_default',
      },
    },
    {
      width: 120,
      field: 'accountingDescription',
      title: '核算说明',
    },
  ],
  data: [],
  // 移除右键菜单配置
  // menuConfig: {
  //   body: {
  //     options: [],
  //   },
  // },
});
const currentDetail = ref();
function handDetailListCellClick({ row }: { row: any }) {
  currentDetail.value = row;
}
const businessCostAccountTreeList = ref([]);
// 获取业务成本科目树
async function getBusinessCostAccount(businessVersionId: string) {
  businessCostAccountTreeList.value =
    await TreeBusinessCostCategoryDetails(businessVersionId);
}
// 业务成本科目树选中
function filterBusinessCostAccountLabel(ids: [string]) {
  return getNamesFromTreeByIds(businessCostAccountTreeList.value, ids);
}
const filterNodeMethod = (value: string, data: any) =>
  data.name.includes(value);

async function handleRightEditClose({ row }: any) {
  let res = null;
  res = await UpdateCostDicDetail(row.id, {
    name: row.name,
    code: row.code,
    costDictionaryVersionId: row.versionId,
    costDictionaryCategoryId: row.categoryId,
    businessCostSubjectDetailsIds: row.businessCostSubjectDetailIds,
  });
  if (res) ElMessage.success('操作成功！');
  emit('detailRefresh');
}

// 获取明细数据
async function loadDetailData() {
  if (props.dictTypeId && props.versionId && props.categoryId) {
    detailListOptions.data = await SelectDetailsList(
      props.dictTypeId,
      props.versionId,
      props.categoryId,
    );
    await getBusinessCostAccount(props.quoteId);
  }
}
// 移除明细右键删除功能
// async function handleDetailRightContextMenuClick({ menu }: { menu: any }) {
//   // 删除功能已移除
// }

// 添加手动刷新方法
const refreshData = () => loadDetailData();

// 初始加载 + 监听ID变化
onMounted(refreshData);
watch([() => props.categoryId, () => props.versionId], refreshData);

// 暴露方法给父组件
defineExpose({ refreshData });
</script>

<style scoped lang="scss">
:deep(.treeSelect-box) {
  .el-select__wrapper {
    height: 33px !important;
    .el-select__selection {
      height: 25px !important;
    }
  }
}
</style>
