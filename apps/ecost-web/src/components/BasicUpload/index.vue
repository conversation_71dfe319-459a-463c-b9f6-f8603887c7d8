<template>
  <ElUpload
    :drag="props.drag"
    :action="props.uploadUrl"
    :multiple="props.multiple"
    :accept="acceptString"
    :headers="{
      Authorization: `Bear<PERSON> ${token}`,
    }"
    :limit="props.maxCount"
    list-type="picture"
    :show-file-list="true"
    v-model:file-list="internalFileList"
    :before-upload="beforeUpload"
    :on-remove="handleRemove"
    :on-success="handleSuccess"
    :on-exceed="handleExceed"
  >
    <div v-show="props.drag">
      <ElIcon class="el-icon--upload"><UploadFilled /></ElIcon>
      <div class="el-upload__text">将文件拖至此处或 <em>点击上传</em></div>
    </div>

    <ElButton v-show="!props.drag" type="primary" size="small">
      点击上传附件
    </ElButton>

    <template #tip>
      <div class="el-upload__tip">
        支持类型：{{ props.acceptedTypes.join(', ') }}，最大
        {{ props.maxSizeKB }}KB，最多 {{ props.maxCount }} 个文件
      </div>
    </template>
    <template #file="{ file }">
      <div style="width: 100%" class="flex h-full w-full items-center">
        <div>
          <ElImage
            v-if="['png', 'jpg', 'jpeg'].includes(file.suffix)"
            :src="`${file.fileUrl || file.url}`"
            :alt="file.name"
            fit="fill"
            style="width: 30px; height: 30px"
          />
          <IconifyIcon
            v-if="['doc', 'docx'].includes(file.suffix)"
            class="text-3xl"
            icon="vscode-icons:file-type-word"
          />
          <IconifyIcon
            v-if="['xls', 'xlsx'].includes(file.suffix)"
            class="text-3xl"
            icon="vscode-icons:file-type-excel"
          />
          <IconifyIcon
            v-if="['pdf'].includes(file.suffix)"
            class="text-3xl"
            icon="vscode-icons:file-type-pdf2"
          />
          <IconifyIcon
            v-if="['ppt', 'pptx'].includes(file.suffix)"
            class="text-3xl"
            icon="vscode-icons:file-type-powerpoint2"
          />
        </div>
        <div
          class="ml-10 max-w-[200px] overflow-hidden text-ellipsis whitespace-nowrap"
        >
          <ElTooltip placement="top" :content="`${file.name}`">
            {{ `${file.name}` }}
          </ElTooltip>
        </div>
        <div class="ml-auto flex items-center">
          <ElIcon
            class="btn-size"
            v-show="['png', 'jpg', 'jpeg'].includes(file.suffix)"
          >
            <ZoomIn @click="handlePictureCardPreview(file)" />
          </ElIcon>
          <ElIcon class="btn-size ml-3 mr-3">
            <Download @click="handleDownload(file)" />
          </ElIcon>
          <ElIcon class="btn-size">
            <Delete @click="handleRemove(file)" />
          </ElIcon>
        </div>
      </div>
    </template>
  </ElUpload>
  <ElImage
    class="vierImage"
    ref="elPreviewImageRef"
    preview-teleported
    :preview-src-list="preViewSrcList"
    show-progress
    fit="cover"
    src=""
  />
</template>

<script setup lang="ts">
import { computed, defineEmits, ref, watch } from 'vue';

import { IconifyIcon } from '@vben/icons';
import { useAccessStore } from '@vben/stores';

import {
  Delete,
  Download,
  UploadFilled,
  ZoomIn,
} from '@element-plus/icons-vue';
import {
  ElButton,
  ElIcon,
  ElImage,
  ElMessage,
  ElTooltip,
  ElUpload,
} from 'element-plus';

import { fileDownload } from '#/api/commonApi';
import { downloadFile } from '#/utils/index';

const props = defineProps({
  uploadUrl: {
    type: String,
    default: 'https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15',
  },
  fileList: {
    type: Array as () => UploadFile[],
    default: () => [],
  },
  acceptedTypes: {
    type: Array as () => string[],
    default: () => ['.pdf', '.pptx', '.docx', '.xlsx', '.jpg', '.png', '.jpeg'],
  },
  maxSizeKB: {
    type: Number,
    default: 500, // 单位 KB
  },
  maxCount: {
    type: Number,
    default: 1,
  },
  multiple: {
    type: Boolean,
    default: true,
  },
  drag: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['update:fileList']);
const elPreviewImageRef = ref();
const preViewSrcList = ref([]);
const accessStore = useAccessStore();

const token = computed(() => {
  return accessStore.getAccessToken();
});

type UploadFile = {
  [key: string]: any;
  name: string;
  raw?: File;
  url?: string;
};

const internalFileList = ref<UploadFile[]>([...props.fileList]);

watch(
  () => props.fileList,
  (newVal) => {
    internalFileList.value = [...newVal];
  },
  { deep: true },
);
const getFileExtension = (filename: string) =>
  filename.slice(filename.lastIndexOf('.')).toLowerCase();

const beforeUpload = (file: File) => {
  const ext = getFileExtension(file.name);
  const isAllowedExt = props.acceptedTypes.includes(ext);

  if (!isAllowedExt) {
    ElMessage.warning(`不支持该文件类型：${ext}`);
    return false;
  }

  const isAllowedSize = file.size / 1024 <= props.maxSizeKB;
  if (!isAllowedSize) {
    ElMessage.warning(`文件大小不能超过 ${props.maxSizeKB}KB`);
    return false;
  }

  if (internalFileList.value.length >= props.maxCount) {
    ElMessage.warning(`最多只能上传 ${props.maxCount} 个文件`);
    return false;
  }

  return true;
};

const handleExceed = () => {
  ElMessage.warning(`最多只能上传 ${props.maxCount} 个文件`);
};

function handlePictureCardPreview(file: UploadFile) {
  const url = file?.url || file?.fileUrl || file?.response?.url;
  if (!url || typeof url !== 'string') {
    ElMessage.warning('无法预览：未找到有效的文件 URL');
    return;
  }
  preViewSrcList.value = [url];
  elPreviewImageRef.value!.showPreview();
}

// const handleRemove = (file: UploadFile, fileList_: UploadFile[]) => {
//   internalFileList.value = fileList_;
//   emit('update:fileList', [...fileList_]);
// };

const handleRemove = (file: UploadFile) => {
  internalFileList.value = internalFileList.value.filter(
    (f) => f.uid !== file.uid,
  );
  emit('update:fileList', [...internalFileList.value]);
};
async function handleDownload(file: any) {
  const name = file.fileName || file.response.fileName;
  const res = await fileDownload(name);
  if (res) {
    downloadFile(res, `${file.name}.${file.suffix}`);
  }
}

const acceptString = computed(() => props.acceptedTypes.join(','));

const handleSuccess = (
  response: any,
  file: UploadFile,
  fileList_: UploadFile[],
) => {
  file.url = response.url || file.url;
  file.suffix = response.suffix;
  internalFileList.value = fileList_;
  emit('update:fileList', [...fileList_]);
};
</script>

<style scoped lang="scss">
.btn-size {
  width: 20px;
  font-size: 20px;
  :hover {
    cursor: pointer;
  }
}
.vierImage {
  :deep(.el-image__error) {
    display: none;
  }
}
</style>
