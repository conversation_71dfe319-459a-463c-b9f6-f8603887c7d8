<template>
  <ElDialog
    v-model="dialogVisible"
    :destroy-on-close="false"
    :title="title"
    @close="dialogClosed"
    top="2%"
    :style="{ width: '80%' }"
  >
    <TransferSelector
      v-model:selection-data="selectionData"
      :choice-class-data="choiceClassData"
      :choice-detail-data="choiceDetailData"
      @select="classSelect"
    />

    <template #footer>
      <ElButton @click="dialogClosed">取消</ElButton>
      <ElButton type="primary" @click="submit">确定</ElButton>
    </template>
  </ElDialog>
</template>
<script lang="ts" setup>
import type { addContractCompilationType } from '#/api/enterpriseCenter/materialManagement/materialContract';

import { inject, onBeforeMount, ref, watch } from 'vue';

import { ElButton, ElDialog, ElMessage } from 'element-plus';

import {
  addContractMaterialDetail,
  getMaterialCategoryList,
  getMaterialDetailList,
} from '#/api/enterpriseCenter/materialManagement/materialContract';
import TransferSelector from '#/components/TransferSelector/index.vue';

export interface addOrEditFormType extends addContractCompilationType {
  id?: null | string;
}

const props = withDefaults(
  defineProps<{
    contractInfoData: any;
    title: string;
    visible: boolean;
  }>(),
  {
    title: '',
    visible: false,
    contractInfoData: {
      parentId: null,
      contractId: '',
      contractTemplateType: '',
    },
  },
);

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
}>();
// 选择区的分类数据
const choiceClassData = ref<any>([]);
// 选择区的明细数据
const choiceDetailData = ref<any>([]);
// 确认区的数据
const selectionData = inject<any>('goodList');
// 传递的表单
const contractInfo = ref(props.contractInfoData);
watch(
  () => props.contractInfoData,
  (nval) => {
    contractInfo.value = nval;
  },
  { deep: true, immediate: true },
);

// selectionData.value = inject('goodList');
// watch(selections!, (nval) => {
//   selectionData.value = nval
// }, { immediate: true })
// selectionData.value = selections.value;

// console.log(selectionData.value)

const dialogVisible = ref(props.visible);
watch(
  () => props.visible,
  (nval) => {
    dialogVisible.value = nval;
  },
  { deep: true, immediate: true },
);

async function getTransferData() {
  const materialContractId = contractInfo.value.contractId;
  const res = await getMaterialCategoryList(materialContractId);
  choiceClassData.value = res || [];
}

async function classSelect(row: any) {
  getDetailList(row.id);
}

async function getDetailList(materialDictionaryCategoryId: string) {
  const materialContractId = contractInfo.value.contractId;
  const params = {
    materialContractId,
    materialDictionaryCategoryId,
  };

  const ids = new Set(selectionData.value.map((v: any) => v.id));
  const res = await getMaterialDetailList(params);
  const data = res.map((item: any) => {
    const selected = !!ids.has(item.id);
    return {
      ...item,
      selected,
    };
  });
  choiceDetailData.value = res ? data : [];
}

// 关闭弹窗
function dialogClosed() {
  emit('update:visible', false);
}

// 提交
const submit = async () => {
  const filterSelectionData = selectionData.value.filter(
    (v: any) => !v.disabled,
  );
  const list = filterSelectionData.map((item: any) => {
    return {
      materialDictionaryVersionId: item.materialDictionaryVersionId,
      materialDictionaryCategoryId: item.materialDictionaryCategoryId,
      materialDictionaryDetailId: item.id,
      unit: item.meteringUnit,
    };
  });

  if (list.length <= 0) {
    ElMessage.warning('请先添加材料');
    return;
  }
  const params = {
    list,
    materialContractId: contractInfo.value.contractId,
    contractTemplateType: contractInfo.value.contractTemplateType,
  };
  const res = await addContractMaterialDetail(params);

  if (res) {
    ElMessage.success('添加成功');
    emit('refresh');
    emit('update:visible', false);
  }
};
// 初始化
async function init() {
  getTransferData();
}

onBeforeMount(async () => {
  init();
});
</script>
<style scoped lang="scss"></style>
