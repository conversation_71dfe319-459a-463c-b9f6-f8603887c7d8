import { ref } from 'vue';
/**
 * 分列组件配置
 */
export const colPageProps = ref({
  leftCollapsedWidth: 3,
  leftCollapsible: true,
  leftMaxWidth: 30,
  leftMinWidth: 20,
  leftWidth: 30,
  resizable: true,
  rightWidth: 70,
  splitHandle: true,
  splitLine: true,
  autoContentHeight: true,
});

// 版本启用状态
export enum EnableStatus {
  DISABLED = 'DISABLED', //  已停用
  ENABLED = 'ENABLED', //  已启用
  NOT_ENABLED = 'NOT_ENABLED', //  未启用
}
// 版本启用状态TEXT
export enum EnableStatusText {
  DISABLED = '已停用',
  ENABLED = '已启用',
  NOT_ENABLED = '未启用',
}

// 材料类型枚举
export enum MaterialType {
  CONCRETE = 'CONCRETE', // / 商品混凝土
  CONSUME_MATERIAL = 'CONSUME_MATERIAL', // / 消耗材料
  FIXEDASSETSL_CONSUMABLES = 'FIXEDASSETSL_CONSUMABLES', // / 固定资产/低值易耗品
  TURNOVERME_MATERIAL = 'TURNOVERME_MATERIAL', // / 周转材料
}

// 材料字典类型枚举TEXT
export enum MaterialTypeText {
  CONCRETE = '商品混凝土',
  CONSUME_MATERIAL = '消耗材料',
  FIXEDASSETSL_CONSUMABLES = '固定资产/低值易耗品',
  TURNOVERME_MATERIAL = '周转材料',
}

// 材料字典
export const materialDict = [
  {
    label: '商品混凝土',
    value: MaterialType.CONCRETE,
  },
  {
    label: '消耗材料',
    value: MaterialType.CONSUME_MATERIAL,
  },
  {
    label: '固定资产/低值易耗品',
    value: MaterialType.FIXEDASSETSL_CONSUMABLES,
  },
  {
    label: '周转材料',
    value: MaterialType.TURNOVERME_MATERIAL,
  },
];
