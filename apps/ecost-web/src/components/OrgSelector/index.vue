<template>
  <div class="org-selector align-center flex w-full">
    <ElTreeSelect v-bind="$attrs" ref="treeSelectRef" v-model="orgId" :data="data" :props="{
      label: 'name',
      disabled: (v: TreeData) => (disabledFiled !== null && v.type === disabledFiled),
    }" :placeholder="placeholder" :disabled="disabled" :default-expand-all="defaultExpandall" :multiple="multiple"
      :show-checkbox="multiple" :check-on-click-node="multiple" filterable :clearable="clearable" check-strictly
      :filter-node-method="filterNode" node-key="id" :collapse-tags="true" collapse-tags-tooltip :max-collapse-tags="3"
       @remove-tag="handleRemoveClick" @check="handleNodeClick" />
  </div>
</template>
<!-- 组织选择器 -->
<script lang="ts" setup>
import { onBeforeMount, ref, watch } from 'vue';

import { ElTreeSelect } from 'element-plus';
import _ from 'lodash';

import { useCommonStore } from '#/store';
import type { TreeData } from './index.interface';
import { OrgType } from './index.interface';
interface Props {
  disabledProps?: OrgType | null,
  modelValue?: null | string | string[];
  placeholder?: string;
  disabled?: boolean;
  clearable?: boolean;
  multiple?: boolean;
  defaultExpandall?: boolean;
  size?: 'default' | 'large' | 'small';
}

defineOptions({
  name: 'OrgSelector',
});
const props = withDefaults(defineProps<Props>(), {
  disabledProps: null,
  modelValue: null,
  placeholder: '请选择组织',
  disabled: false,
  clearable: false,
  multiple: false,
  defaultExpandall: true,
  size: 'default',
});

const emit = defineEmits(['update:modelValue', 'change']);

const commonStore = useCommonStore();

interface Tree {
  [key: string]: any;
}
const orgId = ref<null | string | string[]>(null);
const disabledFiled = ref<OrgType | null>(null); // 禁用的类型
const data = ref([]);
const treeSelectRef = ref();
// 监听外部值变化
watch(
  () => props.modelValue,
  (val) => {
    orgId.value = val;
  },
  { immediate: true },
);
// 监听外部值变化
watch(
  () => props.disabledProps,
  (nval) => {
    disabledFiled.value = nval;
  },
  { immediate: true },
);
// 监听内部值变化
watch(
  () => orgId.value,
  (newVal, oldVal) => {
    emit('update:modelValue', newVal);
  },
);

// 加载组织树数据
const loadOrgTree = async () => {
  try {
    data.value = commonStore.organizationTree;
  } catch (error) {
    console.error('Failed to load organization tree:', error);
  }
};

function handleNodeClick() {
  const oldVal = _.cloneDeep(orgId.value);
  setTimeout(() => {
    const newVal = orgId.value;
    if (newVal?.length === 0) {
      orgId.value = oldVal;
    } else {
      emit('change', orgId.value);
    }
  }, 0);
}
function handleRemoveClick(item: any) {
  const newVal = orgId.value;
  if (newVal?.length === 0) {
    orgId.value = [item];
  } else {
    setTimeout(() => {
      emit('change', orgId.value);
    }, 0);
  }
}

onBeforeMount(() => {
  loadOrgTree();
});

const filterNode = (value: string, data: Tree) => {
  if (!value) return true;
  return data.name.includes(value);
};
</script>

<style lang="scss" scoped>
.org-selector {
  display: inline-block;
}

.custom-tree-select .el-select-dropdown {
  height: 40px;
  /* 固定下拉框高度 */
  overflow-y: auto;
  /* 如果内容超过高度，显示滚动条 */
}
</style>
