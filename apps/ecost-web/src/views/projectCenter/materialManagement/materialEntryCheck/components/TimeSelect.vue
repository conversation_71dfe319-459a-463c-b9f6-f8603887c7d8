<template>
  <VxeGrid v-bind="tableOptions" v-on="tableEvents">
    <template #top>
      <ElDatePicker
        class="mb-2"
        v-model="time"
        type="date"
        placeholder="请选择日期"
        size="default"
      />
    </template>
    <template #seq="{ $rowIndex }">
      <div>{{ $rowIndex + 1 }}</div>
    </template>
  </VxeGrid>
</template>

<script setup lang="ts">
import { onBeforeMount, reactive, ref } from 'vue';

import dayjs from 'dayjs';
import { ElDatePicker } from 'element-plus';

import { getTimeList } from '#/api/enterpriseCenter/materialEntryCheck/materialContract';
import { vxeBaseConfig } from '#/utils/vxeTool';

const emit = defineEmits<{
  (e: 'select', row: any): void;
}>();

const staticItem = {
  id: '',
  name: '全部',
  disabled: true,
  editable: true,
};
const columns = [
  {
    field: 'name',
    title: '时间',
    treeNode: true,
  },
];

const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  columns,
  data: [],
});
const tableEvents = {
  cellClick({ row }: any) {
    emit('select', row);
  },
};
const time = ref();
async function getList() {
  tableOptions.loading = true;
  const res = await getTimeList();
  tableOptions.loading = false;
  const resData = res.map((item: any) => {
    // const time = item.id.replaceAll('_', '/');
    const time = `${item.year}/${item.month}/${item.day}`;
    let name = dayjs(time).format('YYYY年MM月');

    if (item.parentId) {
      name = dayjs(time).format('DD日');
    }
    // console.log(time);
    // console.log();
    return {
      ...item,
      name,
    };
  });
  tableOptions.data = [staticItem, ...resData];
  // tableOptions.data = data.map((item) => {
  //   return {
  //     ...item,
  //     name: item.id,
  //   };
  // });
  console.log(res);
}

async function init() {
  getList();
}

onBeforeMount(async () => {
  await init();
});
</script>
