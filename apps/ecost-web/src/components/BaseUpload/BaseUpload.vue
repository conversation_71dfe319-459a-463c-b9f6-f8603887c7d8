<template>
  <div>
    <ElUpload
      v-model:file-list="fileListData"
      :drag="props.drag"
      list-type="picture-card"
      :before-upload="beforeUpload"
      :on-remove="handleRemove"
      :on-success="handleSuccess"
      :on-exceed="handleExceed"
      :http-request="customUpload"
      method="put"
    >
      <div class="flex h-full flex-col items-center justify-center">
        <ElIcon style="font-size: 32px">
          <UploadFilled class="text-gray-500" />
        </ElIcon>
        <div class="mt-[6px] text-[12px] text-gray-500">
          将文件拖到此处,或
          <div class="text-blue-500">点击上传</div>
        </div>
      </div>
      <template #file="{ file }">
        <div class="flex h-full w-full flex-col justify-between p-2">
          <div class="flex items-center justify-end gap-2">
            <ElButton
              size="small"
              type="success"
              v-show="getFileType(file) === 'image'"
              @click="getImageText(file)"
            >
              AI提取
            </ElButton>
            <ElIcon
              size="16"
              v-show="getFileType(file) === 'image'"
              class="cursor-pointer hover:text-blue-500"
            >
              <ZoomIn @click="handlePictureCardPreview(file)" />
            </ElIcon>
            <ElIcon size="16" class="cursor-pointer hover:text-blue-500">
              <Download @click="handleDownload(file)" />
            </ElIcon>
            <ElIcon size="16" class="cursor-pointer hover:text-blue-500">
              <Delete @click="handleRemove(file)" />
            </ElIcon>
          </div>
          <div class="content flex justify-center">
            <ElImage
              class="h-[58px] w-auto object-contain"
              v-if="getFileType(file) === 'image'"
              :src="file.url"
              :alt="file.name"
              fit="fill"
            >
              <template #placeholder>
                <div
                  class="flex h-full w-full items-center justify-center bg-gray-100"
                >
                  <ElIcon class="animate-spin text-xl text-gray-400">
                    <Loading />
                  </ElIcon>
                </div>
              </template>
            </ElImage>
            <IconifyIcon
              v-if="getFileType(file) === 'word'"
              class="text-7xl"
              icon="vscode-icons:file-type-word"
            />
            <IconifyIcon
              v-if="getFileType(file) === 'excel'"
              class="text-7xl"
              icon="vscode-icons:file-type-excel"
            />
            <IconifyIcon
              v-if="getFileType(file) === 'pdf'"
              class="text-7xl"
              icon="vscode-icons:file-type-pdf2"
            />
            <IconifyIcon
              v-if="getFileType(file) === 'ppt'"
              class="text-7xl"
              icon="vscode-icons:file-type-powerpoint2"
            />
          </div>
          <div class="info text-center">
            <ElTooltip :content="file.name" placement="top">
              <div
                class="name max-w-[200px] overflow-hidden truncate whitespace-nowrap text-[14px]"
              >
                {{ file.name }}
              </div>
            </ElTooltip>
            <div class="size text-[12px]">
              {{ formatFileSize(file.size ?? 0) }}
            </div>
          </div>
        </div>
      </template>
    </ElUpload>

    <ElDialog v-model="imageShowVisible">
      <div class="flex justify-center">
        <img class="max-h-[600px]" :src="currentImageUrl" alt="Preview Image" />
      </div>
    </ElDialog>

    <ElDialog v-model="imageTextShowVisible">
      <div class="flex gap-4">
        <div class="flex w-[70%] justify-center">
          <img
            class="max-h-[600px]"
            :src="currentImageUrl"
            alt="Preview Image"
          />
        </div>
        <div class="w-[30%]">loram</div>
      </div>
    </ElDialog>
  </div>
</template>

<script lang="ts" setup>
import type { UploadFile } from 'element-plus';

import { ref, watch } from 'vue';

import { IconifyIcon } from '@vben/icons';

import {
  Delete,
  Download,
  Loading,
  UploadFilled,
  ZoomIn,
} from '@element-plus/icons-vue';
import axios from 'axios';
import {
  ElButton,
  ElDialog,
  ElIcon,
  ElImage,
  ElMessage,
  ElTooltip,
  ElUpload,
} from 'element-plus';

import { fileCloudUpload, getFileCloudUrl } from '#/api/couldApi';
import { downloadUrlFile } from '#/utils';
import { formatFileSize, getFileType } from '#/utils/common';

interface FileListType {
  id: string;
  key: string;
  url: string;
  size: number;
}

defineOptions({
  name: 'BaseUpload',
});

const props = defineProps({
  fileList: {
    type: Array as () => FileListType[],
    default: () => [],
  },
  acceptedTypes: {
    type: Array as () => string[],
    default: () => [
      '.pdf',
      '.pptx',
      '.docx',
      '.doc',
      '.xlsx',
      '.jpg',
      '.png',
      '.jpeg',
      '.txt',
    ],
  },
  maxSizeKB: {
    type: Number,
    default: 100_000, // 单位 KB
  },
  maxCount: {
    type: Number,
    default: 1,
  },
  multiple: {
    type: Boolean,
    default: true,
  },
  drag: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits<{
  (e: 'update:fileList', fileList: any): void;
  (e: 'success', data: any): void;
  (e: 'remove', file: any): void;
}>();

// 上传地址
const uploadUrl = ref('');
const uploadHeaders = ref<any>();
const fileKey = ref(''); // 文件Key

const fileListData = ref<any[]>([]);
watch(
  () => props.fileList,
  (nval) => {
    fileListData.value = nval;
  },
);

const currentImageUrl = ref('');
const imageShowVisible = ref(false);
const imageTextShowVisible = ref(false);
const handleRemove = (file: any) => {
  emit('remove', file);
};

// 点击下载
const handleDownload = async (file: any) => {
  let url = '';
  if (file.url) {
    url = file.url;
  } else {
    const fileKey = [file.key];
    const res = await getFileCloudUrl(fileKey);
    url = res[file.key];
  }
  await downloadUrlFile(url, file.name);
};

// 上传行为
const customUpload = async (option: any) => {
  const { file, onProgress, onSuccess, onError } = option;
  const res = await fileCloudUpload(file, { isUpload: false });
  uploadUrl.value = res.SignedUrl;
  uploadHeaders.value = res.ActualSignedRequestHeaders;
  fileKey.value = res.fileKey;

  // const action = option.action;
  try {
    const response = await axios.put(res.SignedUrl, file, {
      headers: res.ActualSignedRequestHeaders,
      onUploadProgress: (progressEvent) => {
        const total = progressEvent?.total ?? 0;
        const percent = Math.round((progressEvent.loaded * 100) / total);
        onProgress({ percent });
      },
    });
    const fileKey = [file.key];
    const urlRes = await getFileCloudUrl(fileKey);
    const url = urlRes[file.key];
    const fileData = {
      id: '',
      key: res.fileKey,
      url,
      size: file.size,
    };
    const fileList_: any = [];
    // onSuccess出现了触发两次成功的情况，暂时不清楚为何触发 临时解决办法 不触发onSuccess只调用一次
    // option.onSuccess('ok');
    // onSuccess({ response, file: fileData, fileList_ });
  } catch (error) {
    onError(error);
  }
};

// 获取图片展示
const handlePictureCardPreview = (file: any) => {
  currentImageUrl.value = file.url!;
  imageShowVisible.value = true;
};

// 获取图片文字
const getImageText = (file: any) => {
  currentImageUrl.value = file.url!;
  imageTextShowVisible.value = true;
};

const beforeUpload = async (file: File) => {
  const ext = getFileExtension(file.name);
  const isAllowedExt = props.acceptedTypes.includes(ext);

  if (!isAllowedExt) {
    ElMessage.warning(`不支持该文件类型：${ext}`);
    return false;
  }

  const isAllowedSize = file.size / 1024 <= props.maxSizeKB;
  if (!isAllowedSize) {
    ElMessage.warning(`文件大小不能超过 ${props.maxSizeKB}KB`);
    return false;
  }
  return true;
};
// 上传成功
const handleSuccess = (
  response: any,
  file: UploadFile,
  fileList_: UploadFile[],
) => {
  // fileListData.value = [...fileList_];
  const idx = file.name.lastIndexOf('.');
  const ext = idx === -1 ? '' : file.name.slice(idx + 1);
  const data = {
    fileName: file.name,
    fileExt: ext,
    fileKey: fileKey.value,
    fileSize: String(file.size),
    fileContentType: file?.raw?.type,
  };
  // emit('update:fileList', [...fileList_]);
  emit('success', data);
};

// 超出限制
const handleExceed = () => {
  ElMessage.warning(`最多只能上传 ${props.maxCount} 个文件`);
};
// 获取文件后缀
const getFileExtension = (filename: string) =>
  filename.slice(filename.lastIndexOf('.')).toLowerCase();
</script>
