import { ElMessage } from 'element-plus';

import { infraCloudServiceRequestClient } from '#/api/request';

const product = 'ecost'; // 设置产品名称
/**
 * 上传 华为云
 * @param {file}
 * @returns
 */

type optionsType = {
  fileKey?: null | string; // 是否手动设置文件名称
  isUpload?: boolean | null; // 是否上传文件
};
export async function fileCloudUpload(file: File, options?: optionsType) {
  type filePreParams = {
    ext: string;
    fileContentType: string;
    fileKey?: string; // 如果传递了 会存储为我的名字,如果不传则生成随机名字且需要传递ext
    method: string;
    product: string;
  };
  const { isUpload = false, fileKey = null } = options ?? {};
  const idx = file.name.lastIndexOf('.');
  const ext = idx === -1 ? '' : file.name.slice(idx + 1);
  const params: filePreParams = {
    ext,
    product,
    method: 'put',
    fileContentType: file.type,
  };
  if (fileKey) params.fileKey = fileKey;
  const res = await infraCloudServiceRequestClient.get('/obs/access-url/put', {
    params,
    responseReturn: 'raw', // 临时指定
  });
  const { data } = res;
  const ActualSignedRequestHeaders = data.ActualSignedRequestHeaders;
  const SignedUrl = data.SignedUrl;

  // 是否获取到地址后自动上传
  if (isUpload) {
    await fetch(SignedUrl, {
      method: 'PUT',
      headers: ActualSignedRequestHeaders || {},
      body: file, // 直接传 file
    }).catch((error) => {
      ElMessage.error('上传失败请重新上传');
      throw error;
    });

    return data;
  } else {
    return data;
  }
}

/**
 * 根据华为云文件obsFileKey 获取fileId
 * @param {file}
 * @returns
 */

export async function getFileCloudUrl(fileKeys: string[]) {
  const data = {
    product,
    fileKeys,
  };
  const res = await infraCloudServiceRequestClient.post(
    '/obs/access-url/_access',
    data,
  );
  return res;
}

/**
 * 根据华为云文件obsFileKey 获取fileId
 * @param {file}
 * @returns
 */

type getWpsFileIdParams = {
  create_time: number;
  fileContentType: string;
  fileKey: string;
  modify_time: number;
  name: string;
  productCode: string;
  size: number;
};
export function getWpsFileId(data: getWpsFileIdParams) {
  return infraCloudServiceRequestClient.post('/wps/prepare-edit', data);
}
