<template>
  <div class="annex">
    <div>
      <BaseUpload
        v-model:file-list="fileList"
        @success="addContractAnnex"
        @remove="delContractAnnex"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onBeforeMount, ref, watch } from 'vue';

import { ElMessage, ElMessageBox } from 'element-plus';

import { getFileCloudUrl } from '#/api/couldApi';
import {
  addContractAccessory,
  delContractAccessory,
  getContractAccessoryList,
} from '#/api/enterpriseCenter/materialManagement/materialContract';
import BaseUpload from '#/components/BaseUpload/BaseUpload.vue';
//   delContractAccessory,

defineOptions({
  name: 'Annex',
});

const props = withDefaults(
  defineProps<{
    contractInfoData: any;
  }>(),
  {
    contractInfoData: {
      parentId: null,
      contractId: null,
      contractTemplateType: null,
      goodsList: [],
    },
  },
);

const fileList = ref([]);
// 合同的表单
const contractInfo = ref(props.contractInfoData);
watch(
  () => props.contractInfoData,
  (nval) => {
    contractInfo.value = nval;
  },
);

async function init() {
  getList();
}

async function delContractAnnex(data: any) {
  ElMessageBox.confirm('你确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'error',
  }).then(async () => {
    const id = data.id;
    const res = await delContractAccessory(id);
    if (res) {
      ElMessage.success('删除成功');
      getList();
    }
  });
}
async function addContractAnnex(data: any) {
  const contractId = contractInfo.value.contractId;
  const params = { materialContractId: contractId, ...data };
  const res = await addContractAccessory(params);

  if (res) {
    ElMessage.success('添加成功');
    getList();
  }
}
async function getList() {
  const contractId = contractInfo.value.contractId;
  const res = await getContractAccessoryList(contractId);
  // 获取地址
  const fileKeys = res.map((v: any) => {
    return v.fileKey;
  });
  const urlData = await getFileCloudUrl(fileKeys);
  const data = res.map((v: any) => {
    return {
      size: Number(v.fileSize),
      name: v.fileName,
      key: v.fileKey,
      url: urlData[v.fileKey],
      ...v,
    };
  });
  fileList.value = data;
}

onBeforeMount(() => {
  init();
});
</script>
