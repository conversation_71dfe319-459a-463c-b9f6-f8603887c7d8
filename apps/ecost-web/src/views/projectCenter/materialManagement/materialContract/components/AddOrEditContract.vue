<template>
  <ElDialog
    v-bind="$attrs"
    v-model="dialogVisible"
    :destroy-on-close="true"
    :width="600"
    :title="title"
    :foot="false"
    @close="dialogClosed"
  >
    <ElForm
      :model="addOrEditForm"
      ref="formEl"
      :rules="formRules"
      label-width="160px"
    >
      <div v-if="!isPatchContract">
        <ElFormItem label="合同名称" prop="name">
          <ElInput v-model="addOrEditForm.name" placeholder="请输入合同名称" />
        </ElFormItem>
        <ElFormItem label="合同编码" prop="code">
          <ElInput v-model="addOrEditForm.code" placeholder="请输入合同编码" />
        </ElFormItem>
        <ElFormItem label="选择甲方" prop="partyA">
          <ElSelect v-model="addOrEditForm.partyA" :disabled="isEdit">
            <ElOption
              v-for="v in projectAOption"
              :key="v.id"
              :value="v.id"
              :label="v.name"
            />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="选择乙方" prop="partyB">
          <ElSelect
            v-model="addOrEditForm.partyB"
            @change="partyBSelectChange"
            :disabled="isEdit"
          >
            <ElOption
              v-for="v in projectBOption"
              :key="v.id"
              :value="v.id"
              :label="v.name"
            />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="选择合同范本" prop="contractTemplateId">
          <ElSelect
            v-model="addOrEditForm.contractTemplateId"
            :disabled="isEdit"
          >
            <ElOption
              v-for="v in contractTemplateOption"
              :key="v.id"
              :value="v.id"
              :label="v.name"
            />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="拟定状态" prop="proposedStatus">
          <ElSelect v-model="addOrEditForm.proposedStatus">
            <ElOption :value="ProposedStatus.OFFICIAL" label="正式合同" />
            <ElOption :value="ProposedStatus.PROVISIONAL" label="暂定合同" />
          </ElSelect>
        </ElFormItem>
      </div>

      <div v-else>
        <ElFormItem label="补充协议名称" prop="name">
          <ElInput v-model="addOrEditForm.name" placeholder="请输入合同名称" />
        </ElFormItem>
        <ElFormItem label="补充协议编码" prop="code">
          <ElInput v-model="addOrEditForm.code" placeholder="请输入合同编码" />
        </ElFormItem>
        <ElFormItem label="补充协议范本" prop="contractTemplateId">
          <ElSelect
            v-model="addOrEditForm.contractTemplateId"
            :disabled="isEdit"
          >
            <ElOption
              :value="addOrEditForm.contractTemplateId"
              label="空白范本(系统内置)"
            />
          </ElSelect>
        </ElFormItem>
      </div>
    </ElForm>
    <template #footer>
      <div v-loading="contentLoading">
        <ElButton @click="dialogClosed">取消</ElButton>
        <ElButton type="primary" @click="submit">确定</ElButton>
      </div>
    </template>
  </ElDialog>
</template>
<script lang="ts" setup>
import type { addContractCompilationType } from '#/api/enterpriseCenter/materialManagement/materialContract';

import { computed, onBeforeMount, ref, watch } from 'vue';

import {
  ElButton,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElOption,
  ElSelect,
} from 'element-plus';

import {
  addContractCompilation,
  editContractCompilation,
  getContractTemplateList,
  getPartyACompanyList,
  getPartyBCompanyList,
} from '#/api/enterpriseCenter/materialManagement/materialContract';

export interface addOrEditFormType extends addContractCompilationType {
  id?: null | string;
}
const props = withDefaults(
  defineProps<{
    formData: addOrEditFormType;
    visible: boolean;
  }>(),
  {
    visible: false,
    formData: () => ({
      id: null,
      parentId: null,

      name: '', // 合同名称
      code: '', // 合同编号
      partyA: '', // 甲方id
      partyB: '', // 乙方id
      partyBType: '', // 乙方公司类型
      contractTemplateId: '', // 合同范本id
      proposedStatus: '', // 拟定状态
    }),
  },
);
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
}>();

// 拟定状态
const ProposedStatus = {
  OFFICIAL: 'OFFICIAL', // 正式合同
  PROVISIONAL: 'PROVISIONAL', // 暂存合同
} as const;
const dialogVisible = ref(props.visible);
watch(
  () => props.visible,
  (nval) => {
    dialogVisible.value = nval;
  },
  { deep: true, immediate: true },
);
const isEdit = computed(() => {
  return !!addOrEditForm.value.id;
});

const isPatchContract = computed(() => {
  return !!addOrEditForm.value.parentId;
});

const title = computed(() => {
  if (addOrEditForm.value.parentId) {
    return '新增/编辑补充协议';
  } else {
    return addOrEditForm.value.id ? '编辑合同信息' : '新增合同';
  }
});
// 表单数据
const formEl = ref();

const addOrEditForm = ref<addOrEditFormType>(props.formData);
const contractTemplateId = ref();
watch(
  () => props.formData,
  (nval) => {
    contractTemplateId.value = addOrEditForm.value.contractTemplateId;
    addOrEditForm.value = { ...nval };
  },
  { deep: true, immediate: true },
);
const formRules = ref({
  name: [
    {
      required: true,
      message: addOrEditForm.value.parentId
        ? '请输入协议名称'
        : `请输入合同名称`,
      trigger: ['blur', 'change'],
    },
  ],
  code: [
    {
      required: true,
      message: addOrEditForm.value.parentId
        ? '请输入协议编码'
        : `请输入合同编码`,
      trigger: ['blur', 'change'],
    },
  ],
  partyA: [
    {
      required: true,
      message: `请选择甲方`,
    },
  ],
  partyB: [
    {
      required: true,
      message: `请选择乙方`,
    },
  ],
  contractTemplateId: [
    {
      required: true,
      message: addOrEditForm.value.parentId
        ? '请选择补充协议范本'
        : `请选择合同范本`,
    },
  ],
  proposedStatus: [
    {
      required: true,
      message: `请选择拟定状态`,
    },
  ],
});
const projectAOption = ref<any>([]); // 甲方公司数据
const projectBOption = ref<any>([]); // 乙方公司数据
const contractTemplateOption = ref<any>([]); // 合同范本数据
function partyBSelectChange(val: string) {
  const option = projectBOption.value.find((v: any) => v.id === val);
  addOrEditForm.value.partyBType = option.type;
}
// 关闭弹窗
function dialogClosed() {
  emit('update:visible', false);
}
const contentLoading = ref(false);
// 提交
const submit = async () => {
  formEl.value.validate(async (valid: boolean) => {
    const { id, parentId, ...params } = addOrEditForm.value;
    if (valid) {
      // 补充协议
      contentLoading.value = true;
      if (addOrEditForm.value.parentId) {
        if (id) {
          const res = await editContractCompilation(id, {
            parentId,
            name: params.name,
            code: params.code,
            contractTemplateId: params.contractTemplateId,
          }).catch(() => {
            contentLoading.value = false;
          });
          if (res) {
            ElMessage.success('修改成功');

            dialogClosed();
            emit('refresh');
          }
          contentLoading.value = false;
        } else {
          const res = await addContractCompilation({
            parentId,
            ...params,
          }).catch(() => {
            contentLoading.value = false;
          });
          if (res) {
            ElMessage.success('新增成功');
            contentLoading.value = false;
            dialogClosed();
            emit('refresh');
          }
          contentLoading.value = false;
        }
      } else {
        // 合同
        if (id) {
          const res = await editContractCompilation(id, params).catch(() => {
            contentLoading.value = false;
          });
          if (res) {
            ElMessage.success('修改成功');
            dialogClosed();
            emit('refresh');
          }
          contentLoading.value = false;
        } else {
          const res = await addContractCompilation(params).catch(() => {
            contentLoading.value = false;
          });
          if (res) {
            ElMessage.success('新增成功');
            dialogClosed();
            emit('refresh');
          }
          contentLoading.value = false;
        }
      }
    }
  });
};
// 初始化
async function init() {
  projectAOption.value = await getPartyACompanyList({});
  projectBOption.value = await getPartyBCompanyList({});
  contractTemplateOption.value = await getContractTemplateList({});
}

onBeforeMount(async () => {
  init();
});
</script>
<style scoped lang="scss"></style>
