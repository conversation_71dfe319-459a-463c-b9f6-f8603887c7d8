<template>
  <div class="flex">
    <!-- 版本 -->
    <div class="w-64">
      <BasicTitle class="mb-1" title="版本" />
      <vxe-grid
        v-bind="versionOptions"
        ref="versionRef"
        @cell-click="handleVersionCellClick"
        @checkbox-change="handleVersionCheckBoxChange"
      >
        <template #status="{ row }">
          <ElTag
            size="small"
            :type="row.status === 'ENABLED' ? 'success' : 'warning'"
          >
            {{ row.status === 'ENABLED' ? '已发布' : '已停用' }}
          </ElTag>
        </template>
      </vxe-grid>
    </div>

    <!-- 分类 -->
    <div class="ml-2 mr-2 w-96">
      <BasicTitle class="mb-1" title="分类" />
      <vxe-grid
        v-bind="categoryOptions"
        ref="categoryRef"
        @cell-click="handleCategoryCellClick"
      >
        <template #typeSlot="{ row }">
          {{ materialDict.find((item) => item.value === row.type)?.label }}
        </template>
      </vxe-grid>
    </div>

    <!-- 明细 -->
    <div class="w-32 flex-1">
      <BasicTitle class="mb-1" title="明细" />
      <vxe-grid
        v-bind="detailOptions"
        ref="detailRef"
        @cell-click="handleDetailCellClick"
      >
        <template #typeSlot="{ row }">
          {{ materialDict.find((item) => item.value === row.type)?.label }}
        </template>
        <template #businessCostSubjectSlot="{ row }">
          {{ filterBusinessCostAccountLabel(row.businessCostSubjectDetailIds) }}
        </template>
      </vxe-grid>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineEmits, defineProps, nextTick, reactive, ref, watch } from 'vue';

import { ElMessage, ElTag } from 'element-plus';
import _ from 'lodash';

import { TreeBusinessCostCategoryDetails } from '#/api/enterpriseCenter/enterpriseStandards/businessCostSubject';
import {
  CategoryList,
  DetailsList,
  VersionList,
} from '#/api/projectCenter/projectSetting/businessCost';
import BasicTitle from '#/components/BasicTitleBar/index.vue';
import { getNamesFromTreeByIds } from '#/utils/common';

import { materialDict } from '../../data';

const props = withDefaults(
  defineProps<{
    dictType: string;
    dictTypeId: string;
  }>(),
  {
    dictTypeId: '',
    dictType: 'MECHANICAL_DICTIONARY',
  },
);
const emit = defineEmits<{
  (e: 'componentsEvent', payload: Object): void;
}>();

const selectCategoryIds = ref([]); // 已选择的分类
const selectDetails = ref([]); // 已选择版本
const selectVersionId = ref(''); // 已选择的版本ID

const currentVersion = ref({});
const versionRef = ref();
const versionOptions = reactive({
  height: 700,
  size: 'mini',
  border: true,
  align: 'center',
  keepSource: true,
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  columnConfig: {
    resizable: true,
  },
  checkboxConfig: {
    showHeader: false,
    checkMethod: ({ row }: { row: any }) => {
      // 允许勾选的条件：状态不为DISABLED
      return row.status !== 'DISABLED';
    },
  },
  columns: [
    {
      type: 'checkbox',
      width: 40,
    },
    {
      field: 'name',
      title: '名称',
      showOverflow: 'title',
    },
    {
      field: 'orgName',
      title: '编制单位',
    },
    {
      field: 'status',
      title: '状态',
      slots: {
        default: 'status',
      },
    },
  ],
  data: [],
});
async function handleVersionCellClick({ row }) {
  currentVersion.value = row;
  categoryOptions.data = await CategoryList(props.dictTypeId, row.id);
  await nextTick(() => {
    categoryRef.value.setAllTreeExpand(true);
  });
  // 切换版本数据置空
  selectCategoryIds.value = [];
  selectDetails.value = [];
}

function handleVersionCheckBoxChange({ row, records }: any) {
  // 检查是否已经有其他版本被勾选
  const otherSelectedVersions = records.filter(
    (item: any) => item.id !== row.id,
  );

  if (otherSelectedVersions.length > 0) {
    // 如果已经有其他版本被勾选，不允许勾选新版本
    ElMessage.warning('只能选择一个版本！');
    // 取消当前行的勾选
    nextTick(() => {
      versionRef.value.setCheckboxRow(row, false);
    });
    return;
  }

  // 更新选中的版本ID
  selectVersionId.value = records.length > 0 ? records[0].id : '';

  emit('componentsEvent', {
    versionId: selectVersionId.value,
    categoryIds: selectCategoryIds.value,
    details: selectDetails.value,
  });
}
// 分类
const categoryRef = ref();
const categoryOptions = reactive({
  height: 700,
  size: 'mini',
  border: true,
  align: 'center',
  keepSource: true,
  rowConfig: {
    isCurrent: true,
    isHover: true,
    keyField: 'id',
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  columnConfig: {
    resizable: true,
  },
  columns: [
    {
      treeNode: true,
      field: 'code',
      title: '编码',
    },
    {
      field: 'name',
      title: '名称',
    },
    {
      field: 'remark',
      title: '备注',
    },
  ],
  data: [],
});
async function handleCategoryCellClick({ row }) {
  const dictTypeId = props.dictTypeId;
  const versionId = row.versionId;
  const categoryId = row.id;
  detailOptions.data = await DetailsList(dictTypeId, versionId, categoryId);

  // 切换分类数据置空
  selectDetails.value = [];
}

// 明细
const detailRef = ref();
const detailOptions = reactive({
  height: 700,
  size: 'mini',
  border: true,
  align: 'center',
  keepSource: true,
  rowConfig: {
    isCurrent: true,
    isHover: true,
    keyField: 'id',
  },
  columnConfig: {
    resizable: true,
  },
  columns: [
    {
      width: 100,
      title: '编码',
      field: 'code',
    },
    {
      width: 120,
      field: 'name',
      title: '名称',
    },
    {
      field: 'specificationModel',
      title: '规格型号',
    },
    {
      width: 120,
      field: 'remark',
      title: '备注',
    },
    {
      field: 'businessCostSubjectDetailsIds',
      title: '业务成本科目名称',
      slots: {
        default: 'businessCostSubjectSlot',
      },
    },
    {
      width: 120,
      field: 'accountingDescription',
      title: '核算说明',
    },
  ],
  data: [],
});
function handleDetailCellClick() {}

// 版本校验
function validateAll(): boolean {
  if (_.isEmpty(currentVersion.value)) {
    ElMessage.warning('请选择版本！');
    return false;
  }

  if (!selectVersionId.value) {
    ElMessage.warning('请选择版本！');
    return false;
  }

  return true;
}

const businessCostAccountTreeList = ref([]);
// 获取业务成本科目树
async function getBusinessCostAccount(businessVersionId: string) {
  businessCostAccountTreeList.value =
    await TreeBusinessCostCategoryDetails(businessVersionId);
}
// 业务成本科目树选中
function filterBusinessCostAccountLabel(ids: [string]) {
  return getNamesFromTreeByIds(businessCostAccountTreeList.value, ids);
}
watch(
  () => props.dictType,
  () => {
    getVersionList(props.dictTypeId);
    getBusinessCostAccount(currentVersion.value.businessCostSubjectVersionId);
  },
  {
    immediate: true,
  },
);

// 获取分类
async function getVersionList(dictTypeId: string) {
  versionOptions.data = await VersionList(dictTypeId);
}

defineExpose({
  validateAll,
});
</script>

<style scoped lang="scss"></style>
