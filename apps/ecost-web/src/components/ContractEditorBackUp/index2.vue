<template>
  <ElDrawer v-bind="$attrs" v-model="drawerVisible" :modal="false" :destroy-on-close="true" :append-to-body="true"
    class="customer-drawer" modal-class="customer-modal" @close="handleClose" @opened="handleOpen">
    <!-- 头部 -->
    <template #header>
      <div class="layout-header flex h-[60px] items-center justify-between">
        <div class="layout-header-left flex items-center gap-8">
          <div class="flex items-center gap-4">
            <div class="self-center text-sm text-black">合同范本名称</div>
            <ElInput size="default" v-model="contractTemplateName" style="width: 240px" placeholder="" />
          </div>
          <div class="flex items-center gap-4">
            <div class="self-center text-sm text-black">范本说明</div>
            <ElInput size="default" v-model="contractTemplateDesc" style="width: 240px" placeholder="" />
          </div>
        </div>
        <div class="btn-group pr-4">
          <ElButton type="primary" size="small" @click="insureSubmit">提交</ElButton>
        </div>
      </div>
    </template>
    <div class="flex bg-[#fafafa]" style="height: calc(100vh - 70px)">
      <!-- 左边区域 -->
      <div class="flex flex-1 w-[64%] flex-col">
        <!-- 上部（固定高度） -->
        <div class="layout-header">
          <Menu ref="menuEl"></Menu>
        </div>
        <!-- 中间区域（上下已占用，垂直剩余区域） -->
        <div class="flex flex-1 overflow-hidden bg-[#fafafa]">
          <!-- 左部（固定宽度） -->
          <div class="w-[200px]">
            <Catalog ref="catalogEl"> </Catalog>
          </div>
          <!-- 右部（可滚动） -->
          <div class="box-border-box flex-1 overflow-y-scroll bg-[#fafafa] p-4">
            <div class="editor-container" ref="editorEl"></div>
          </div>
        </div>
        <!-- 下部（固定高度） -->
        <div class="h-8 bg-[#fafafa]">
          <Footer ref="footEl"></Footer>
        </div>
      </div>
      <!-- 右侧区域（固定宽度） -->
      <div class="box-border-box flex w-[36%] min-w-[600px] flex-col p-4">
        <!-- <Comment ref="commentEl"></Comment> -->
        <div class="search-container item-center box-border-box mb-4 flex h-[32px] justify-between">
          <div class="btn-group flex items-center gap-4">
            <ElButton type="primary" size="small" link @click="expand('open')">展开</ElButton>
            <ElButton type="primary" size="small" link @click="expand('close')">折叠</ElButton>
          </div>
          <ElInput v-model="searchValue" style="width: 240px" placeholder="" :suffix-icon="Search" />
        </div>

        <div class="filed-table-container mb-2 flex-1 overflow-y-scroll">
          <vxe-grid ref="tableRef" v-bind="tableOptions">
            <template #top>
            </template>
            <template #name="{ row, $rowIndex }">
              {{ row.name }}
              <ElButton v-if="row.children !== null" type="primary" link size="large" :icon="CirclePlus"></ElButton>
            </template>

            <template #isRequired="{ row, $rowIndex }">
              <ElCheckbox v-if="row.parentId !== null" v-model="row.isRequire"></ElCheckbox>
            </template>

            <template #fieldType="{ row, $rowIndex }">
              {{ row.fieldType }}
            </template>
            <template #enumValue="{ row, $rowIndex }">
              <!-- {{ row.enumValue }} -->
              <div v-if="row.parentId !== null">
                <el-input v-if="row.fieldType == 'TEXT'" v-model="row.value" style="width: 100%" />
                <el-input-number v-if="row.fieldType == 'NUMBER'" v-model="row.value" style="width: 100%" />
                <el-input-number v-if="row.fieldType == 'PERCENT'" style="width: 100%" v-model="row.value"
                  :precision="2" :step="0.1" :max="10" />
                <el-select v-if="row.fieldType == 'ENUM'" v-model="row.value" style="width: 100%">
                  <el-option :label="'测试1'" :value="'1'" />
                  <el-option :label="'测试2'" :value="'2'" />
                  <el-option :label="'测试3'" :value="'3'" />
                </el-select>
                <el-date-picker v-if="row.fieldType == 'DATE'" style="width: 100%" v-model="row.value" type="date" />
                <el-button type="primary" size="small">插入</el-button>
              </div>

            </template>
          </vxe-grid>
        </div>

        <div class="mandatory-item-container h-[220px]  mb-2 w-full ">
          <div class="btn-group mb-2">
            <ElButton type="primary" @click="setBandItem" size="small">设置强制条款</ElButton>
          </div>

          <div class="mandatory-table h-[200px] overflow-y-scroll">
            <ElTable :data="mandatoryTerm" border size="small" style="width: 100%">
              <ElTableColumn prop="name" label="名称" width="180">
                <template #default="scope">
                  <div class="flex items-center gap-4">
                    <div>强制条款 {{ scope.$index + 1 }}</div>
                    <ElButton type="danger" size="small" :icon="Delete" @click="deleteMandatory(scope.row)">删除
                    </ElButton>
                  </div>
                </template>
              </ElTableColumn>
              <ElTableColumn prop="content" label="内容">
                <template #default="scope">
                  <ElPopover effect="light" trigger="hover" placement="top-end" width="240px">
                    <template #default>
                      <div>{{ scope.row.content }}</div>
                    </template>
                    <template #reference>
                      <div class="truncate">{{ scope.row.content }}</div>
                    </template>
                  </ElPopover>
                </template>
              </ElTableColumn>
            </ElTable>
          </div>
        </div>
      </div>
    </div>

    <div class="hidden" ref="editorDockerEl"></div>
    <div class="hidden" ref="previewEl" id="previewEl"></div>
  </ElDrawer>
</template>

<script lang="ts" setup>
import { CirclePlus, Delete, Search } from '@element-plus/icons-vue';
import type { Command, IEditorOption, IElement } from '@hufe921/canvas-editor';
import {
  EditorZone,
  ElementType,
  getElementListByHTML,
  KeyMap,
  RowFlex,
} from '@hufe921/canvas-editor';
import docxPlugin from '@hufe921/canvas-editor-plugin-docx';
import Editor from '@hufe921/canvas-editor/';
import { renderAsync } from 'docx-preview';
import {
  ElButton,
  ElCheckbox,
  ElDatePicker,
  ElDrawer,
  ElInput,
  ElInputNumber,
  ElOption,
  ElPopover,
  ElSelect,
  ElTable,
  ElTableColumn
} from 'element-plus';
import { provide, reactive, ref, watch } from 'vue';
import type { VxeGridProps } from 'vxe-table';
import Catalog from './components/catalog/catalog.vue';
import Footer from './components/footer/footer.vue';
import Menu from './components/menu/menu.vue';
import { Signature } from './components/signature/Signature';
import { TextElementBuilder } from './ft';
import paserDocxPlugin from './plugins/prase/index';
import './style.css';
defineOptions({
  name: 'ContractWord',
});
interface SubmitPayload {
  name: string;
  remark: string;
  file: File
}
const props = withDefaults(
  defineProps<{
    formData: any;
    mandatoryTerm: any;
    fieldRule: any;
    dcoxFile: File | undefined;
    visible: boolean;
  }>(),
  {
    visible: false,
    dcoxFile: undefined,
    formData: () => ({}),
    mandatoryTerm: () => ([]),
    fieldRule: () => ({}),
  },
);
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
  (e: 'submit', payload: SubmitPayload): void;
  (e: 'deleteMandatory', payload: any): void;
  (e: 'setFiled', payload: any): void;
}>();
const previewEl = ref()
const tableRef = ref()
const fieldRule = ref([])
const mandatoryTerm = ref([])
const drawerVisible = ref(false);
const contractTemplateName = ref('');
const contractTemplateDesc = ref('');
const options: IEditorOption = {
  margins: [100, 120, 100, 120],
  maskMargin: [60, 0, 30, 0], // 菜单栏高度60，底部工具栏30为遮盖层
  watermark: {
    data: 'E-Wing',
    size: 200,
  },
  pageNumber: {
    format: '第{pageNo}页/共{pageCount}页',
  },
  placeholder: {
    data: '请输入正文',
  },
  zone: {
    tipDisabled: false,
  },
  group: {
    opacity: 0.4,
    backgroundColor: 'red',
    activeOpacity: 0.6,
    activeBackgroundColor: 'red',
    disabled: false,
    deletable: false,
  }
};
const commentList: any = [
  {
    id: '1',
    content: '测试',
    userName: 'xxx',
    rangeText: 'xxx',
    createdDate: '2023-08-20 23:10:55',
  },
];
declare module '@hufe921/canvas-editor' {
  interface Command {
    executeImportDocx(options: any): any;
  }
}
let textRender: TextElementBuilder; // 渲染器
let editor: any
const editorEl = ref();
const editorDockerEl = ref();
const menuEl = ref();
const footEl = ref();
const catalogEl = ref();
const formData = ref({
  name: '',
  remark: ""
});
const dcoxFile = ref(); //传入文件
// 字段数据

// 强制条款数据
const columns = [
  {
    field: 'name',
    title: '字段名称',
    width: '200',
    slots: {
      default: 'name',
    },
    treeNode: true
  },

  {
    field: 'isRequired',
    title: '必填',
    width: '80',
    slots: {
      default: 'isRequired',
    },
  },
  {
    field: 'fieldType',
    title: '字段类型',
    width: '80',
    slots: {
      default: 'fieldType',
    },
  },
  {
    field: 'enumValue',
    title: '枚举值',
    width: '180',
    slots: {
      default: 'enumValue',
    },
  }
];
const tableOptions = reactive<VxeGridProps>({
  size: 'mini',
  height: '100%',
  border: true,
  align: 'center',
  showOverflow: true,
  keepSource: true,
  headerRowClassName: 'bg-[skyblue]',
  rowClassName: ({ row }: { row: { parentId: string | null } }) => {
    if (row.parentId) {
      return '';
    } else {
      return 'bg-gray-100';
    }
  },
  columnConfig: {
    resizable: true,
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  rowConfig: {
    isCurrent: true,
  },
  checkboxConfig: {
    labelField: 'name',
    highlight: true,
  },
  columns,
  data: [],
});
watch(
  () => props.formData,
  (nval) => {
    if (nval) {
      formData.value = { ...nval };
      contractTemplateName.value = formData.value.name
      contractTemplateDesc.value = formData.value.remark
    }
  },
  {
    immediate: true,
    deep: true,
  },
);
watch(
  () => props.fieldRule,
  (nval) => {
    if (nval) {
      fieldRule.value = nval;
      tableOptions.data = fieldRule.value
    }
  },
  {
    immediate: true,
    deep: true,
  },
);
watch(
  () => props.mandatoryTerm,
  (nval) => {
    if (nval) {
      mandatoryTerm.value = nval;
    }
  },
  {
    immediate: true,
    deep: true,
  },
);
watch(
  () => props.dcoxFile,
  (nval) => {
    if (nval) {
      dcoxFile.value = nval;
    }
  },
  {
    immediate: true,
    deep: true,
  },
);
watch(
  () => props.visible,
  (nval) => {
    drawerVisible.value = nval;
  },
);
async function insureSubmit() {
  const res = await editor.command.executeParseDocx({
    fileName: '合同范本',
  });
  let params = {
    name: contractTemplateName.value, // 范本名称
    remark: contractTemplateDesc.value, // 范本说明
    file: res,
  }
  emit('submit', params);
}
function handleClose() {
  drawerVisible.value = false;
  emit('update:visible', false);
}
function handleOpen() {
  initEditor(dcoxFile.value)
}
function delay(time: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, time));
}
type fun = (payload: any) => void;
const listeners: fun[] = [];

provide('registerRangeStyleChange', (cb: fun) => {
  listeners.push(cb);
});
const expand = (type: string) => {
  if (type === 'close') {
    tableRef.value.clearTreeExpand()
  } else {
    tableRef.value.setAllTreeExpand(true)
  }

};
const setBandItem = () => { }

const searchValue = ref('');




function deleteMandatory(row) {
  emit('deleteMandatory', row);
}

function setFiledClick(row) {
  emit('setFiled', row);
}



const initEditor = async (file: File) => {
  await delay(300);
  // 1. 初始化编辑器
  // let tempInstance = new Editor(tempInstanceEl.value, { main: <IElement[]>[], }, options);
  // tempInstance.use(paserDocxPlugin);
  // tempInstance.use(docxPlugin);
  // const fileParse = await importDocxFile(file, tempInstance); // 解析word文档并导入文档
  // const wordText = fileParse?.data.main.map(item => item.value).join('') || '';
  // textRender = new TextElementBuilder(wordText, {
  //   titleText: [],
  //   colorText: ['根据《中华人民共和国民法典》、《中华人民共和国建筑法》及其他有关法律、法规之规定，遵循平等、自愿、公平和诚实信用原则，且乙方在签订合同前对现场环境、施工条件、图纸及有关资料已充分了解，熟知国家、地方关于增值税的相关规定及甲方的项目管理制度、资金状况，并具有一定的经济实力，在此基础上，经双方协商一致，签订本合同，双方共同遵照执行。'],
  //   highlightText: ['签约时间：'],
  // });
  // const data = textRender.getElementList();
  // tempInstance.destroy();
  // 实例化编辑器
  const arrayBuffer = await file.arrayBuffer();
  await renderAsync(arrayBuffer, previewEl.value);
  const htmlString = previewEl.value.innerHTML;
  console.log(htmlString)
  const data = getElementListByHTML(htmlString, { innerWidth: 0 })
  console.log(data)
  // console.log(mandatoryTerm.value)
  const container = editorEl.value;
  const instance = new Editor(
    container,
    {
      header: [
        {
          value: '易盈科技有限公司',
          size: 32,
          rowFlex: RowFlex.CENTER,
        },
        {
          value: '\n合同范本',
          size: 18,
          rowFlex: RowFlex.CENTER,
        },
        {
          value: '\n',
          type: ElementType.SEPARATOR,
        },
      ],
      main: <IElement[]>data,
      footer: [
        // {
        //   value: 'canvas-editor',
        //   size: 12,
        // },
      ],
    },
    options,
  );
  instance.use(paserDocxPlugin);
  instance.use(docxPlugin);
  editor = instance;
  Reflect.set(window, 'editor', instance); // 将instance注册全局
  // const fileParse = await importDocxFile(file, instance); // 解析word文档并导入文档
  // console.log(fileParse)
  // instance.command.executeSetRange(
  //   0,
  //   200)
  // instance.command.executeSetGroup()

  // 初始化菜单
  menuEl.value.init(instance);
  const menuShortcutList = menuEl.value.shortcutList;
  // 初始化左侧栏
  catalogEl.value.init(instance);
  const isCatalogShow = catalogEl.value.isCatalogShow;
  const updateCatalog = catalogEl.value.updateCatalog;
  // 初始化底部工具栏
  footEl.value.init(instance, {
    isCatalogShow,
    updateCatalog
  });

  // 8. 内部事件监听汇总
  instance.listener.rangeStyleChange = function (payload) {
    listeners.forEach((cb) => cb(payload));
  };
  instance.listener.saved = function (payload) {
    console.log('elementList: ', payload);
  };

  // 9. 右键菜单注册
  instance.register.contextMenuList([
    // {
    //   name: '批注',
    //   when: (payload) => {
    //     return (
    //       !payload.isReadonly &&
    //       payload.editorHasSelection &&
    //       payload.zone === EditorZone.MAIN
    //     );
    //   },
    //   callback: (command: Command) => {
    //     new Dialog({
    //       title: '批注',
    //       data: [
    //         {
    //           type: 'textarea',
    //           label: '批注',
    //           height: 100,
    //           name: 'value',
    //           required: true,
    //           placeholder: '请输入批注',
    //         },
    //       ],
    //       onConfirm: (payload) => {
    //         const value = payload.find((p) => p.name === 'value')?.value;
    //         if (!value) return;
    //         const groupId = command.executeSetGroup();
    //         if (!groupId) return;
    //         commentList.push({
    //           id: groupId,
    //           content: value,
    //           userName: 'Hufe',
    //           rangeText: command.getRangeText(),
    //           createdDate: new Date().toLocaleString(),
    //         });
    //       },
    //     });
    //   },
    // },
    {
      name: '设置强制条款',
      icon: 'word-tool',
      when: (payload) => {
        return (
          !payload.isReadonly &&
          payload.editorHasSelection &&
          payload.zone === EditorZone.MAIN
        );
      },
      callback: (command: Command) => {
        const value = command.getRangeContext();
        if (!value) return;
        const groupId = command.executeSetGroup();
        if (!groupId) return;
        console.log({
          id: groupId,
          content: value,
          rangeText: command.getRangeText(),
          createdDate: new Date().toLocaleString(),
        });




        // commentList.push({
        //   id: groupId,
        //   content: value,
        //   userName: 'Hufe',
        //   rangeText: command.getRangeText(),
        //   createdDate: new Date().toLocaleString(),
        // });
      },
    },
    {
      name: '签名',
      icon: 'signature',
      when: (payload) => {
        return !payload.isReadonly && payload.editorTextFocus;
      },
      callback: (command: Command) => {
        new Signature({
          onConfirm(payload) {
            if (!payload) return;
            const { value, width, height } = payload;
            if (!value || !width || !height) return;
            command.executeInsertElementList([
              {
                value,
                width,
                height,
                type: ElementType.IMAGE,
              },
            ]);
          },
        });
      },
    },
    {
      name: '格式整理',
      icon: 'word-tool',
      when: (payload) => {
        return !payload.isReadonly;
      },
      callback: (command: Command) => {
        command.executeWordTool();
      },
    },

  ]);
  // 10. 快捷键注册
  instance.register.shortcutList([
    ...menuShortcutList,
    {
      key: KeyMap.P,
      mod: true,
      isGlobal: true,
      callback: (command: Command) => {
        command.executePrint();
      },
    },
    {
      key: KeyMap.MINUS,
      ctrl: true,
      isGlobal: true,
      callback: (command: Command) => {
        command.executePageScaleMinus();
      },
    },
    {
      key: KeyMap.EQUAL,
      ctrl: true,
      isGlobal: true,
      callback: (command: Command) => {
        command.executePageScaleAdd();
      },
    },
    {
      key: KeyMap.ZERO,
      ctrl: true,
      isGlobal: true,
      callback: (command: Command) => {
        command.executePageScaleRecovery();
      },
    },
  ]);

  async function importDocxFile(file: File, instance) {
    if (!file) return;
    const buffer = await new Promise<ArrayBuffer>((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (event) => {
        const result = event?.target?.result;
        if (result instanceof ArrayBuffer) {
          resolve(result);
        } else {
          reject(new Error("读取结果不是 ArrayBuffer"));
        }
      };
      reader.onerror = () => reject(reader.error);
      reader.readAsArrayBuffer(file);
    });
    await instance.command.executeImportDocx({ arrayBuffer: buffer });
    return instance.command.getValue();
  }
};

</script>

<style lang="scss">
.el-drawer__header {
  margin-bottom: 0;
  padding: 0 16px;
}

.el-drawer__body {
  padding: 0;
}

.customer-drawer {
  width: 100% !important;
}

.customer-modal {
  width: 80% !important;
  min-width: 1200px;
  margin-left: auto;
}
</style>
