import { ecostServiceRequestClient } from '#/api/request';

/**
 * 税率字典 -- 获取版本
 */
export function ListTaxRateDicVersion() {
  return ecostServiceRequestClient.get('/taxratel-dictionary-version');
}

/**
 * 税率字典 -- 新增版本
 * @param data 版本数据
 */
export function AddTaxRateDicVersion(data: any) {
  return ecostServiceRequestClient.post('/taxratel-dictionary-version', data);
}

/**
 * 税率字典 -- 更新版本
 * @param versionId 版本ID
 * @param data 版本数据
 */
export function UpdateTaxRateDicVersion(versionId: string, data: any) {
  return ecostServiceRequestClient.patch(
    `/taxratel-dictionary-version/${versionId}`,
    data,
  );
}

/**
 * 税率字典 -- 删除版本
 * @param versionId 版本ID
 */
export function DelTaxRateDicVersion(versionId: string) {
  return ecostServiceRequestClient.delete(
    `/taxratel-dictionary-version/${versionId}`,
  );
}

/**
 * 税率字典 -- 获取分类列表
 * @param versionId 版本ID
 */
export function ListTaxRateDicCategory(versionId: string) {
  return ecostServiceRequestClient.get(`/taxrate-dictionary-category`, {
    params: {
      versionId,
    },
  });
}

/**
 * 税率字典 -- 创建分类
 * @param data 数据对象
 */
export function AddTaxRateDicCategory(data: any) {
  return ecostServiceRequestClient.post(`/taxrate-dictionary-category`, data);
}

/**
 * 税率字典 -- 更新分类
 * @param categoryId 分类ID
 * @param data 数据对象
 */
export function UpdateTaxRateDicCategory(categoryId: string, data: any) {
  return ecostServiceRequestClient.patch(
    `/taxrate-dictionary-category/${categoryId}`,
    data,
  );
}

/**
 * 税率字典 -- 删除分类
 * @param categoryId 分类ID
 */
export function DeleteTaxRateDicCategory(categoryId: string) {
  return ecostServiceRequestClient.delete(
    `/taxrate-dictionary-category/${categoryId}`,
  );
}

/**
 * 税率字典 -- 移动分类
 * @param categoryId 分类ID
 * @param direction 移动方向 - 'up' or 'down'
 */
export function MoveTaxRateDicCategory(categoryId: string, direction: string) {
  return ecostServiceRequestClient.patch(
    `/taxrate-dictionary-category/${categoryId}/_move?moveTo=${direction}`,
  );
}

/**
 * 税率字典 -- 获取明细列表
 * @param params 查询参数
 */
export function ListTaxRateDicDetail(params: any) {
  return ecostServiceRequestClient.get(`/taxrate-dictionary-detail`, {
    params,
  });
}

/**
 * 税率字典 -- 搜索明细列表
 * @param params 查询参数
 */
export function QueryTaxRateDicDetail(params: any) {
  return ecostServiceRequestClient.get(`/taxrate-dictionary-detail/_search`, {
    params,
  });
}

/**
 * 税率字典 -- 搜索明细列表
 * @param taxrateDictionaryDetailId 查询参数
 */
export function QueryTaxrateDictionaryChangeLog(
  taxrateDictionaryDetailId: string,
) {
  return ecostServiceRequestClient.get(
    `/taxrate-dictionary-detail/record/${taxrateDictionaryDetailId}`,
  );
}

/**
 * 税率字典 -- 创建明细
 * @param data 数据对象
 */
export function AddTaxRateDicDetail(data: any) {
  return ecostServiceRequestClient.post(`/taxrate-dictionary-detail`, data);
}

/**
 * 税率字典 -- 更新明细
 * @param detailId 明细ID
 * @param data 数据对象
 */
export function UpdateTaxRateDicDetail(detailId: string, data: any) {
  return ecostServiceRequestClient.patch(
    `/taxrate-dictionary-detail/${detailId}`,
    data,
  );
}

/**
 * 税率字典 -- 删除明细
 * @param detailId 明细ID
 */
export function DeleteTaxRateDicDetail(detailId: string) {
  return ecostServiceRequestClient.delete(
    `/taxrate-dictionary-detail/${detailId}`,
  );
}

/**
 * 税率字典 -- 移动明细
 * @param detailId 明细ID
 * @param direction 移动方向 - 'up' or 'down'
 */
export function MoveTaxRateDicDetail(detailId: string, direction: string) {
  return ecostServiceRequestClient.patch(
    `/taxrate-dictionary-detail/${detailId}/_move`,
    {},
    { moveTo: direction },
  );
}
