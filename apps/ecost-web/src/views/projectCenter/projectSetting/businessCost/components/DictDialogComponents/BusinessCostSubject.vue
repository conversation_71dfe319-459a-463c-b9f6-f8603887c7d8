<template>
  <div class="flex">
    <!-- 版本 -->
    <div class="w-64">
      <BasicTitle class="mb-1" title="版本" />
      <vxe-grid
        v-bind="versionOptions"
        ref="versionRef"
        @cell-click="handleVersionCellClick"
        @checkbox-change="handleVersionCheckBoxChange"
      >
        <template #status="{ row }">
          <ElTag
            size="small"
            :type="row.status === 'ENABLED' ? 'success' : 'warning'"
          >
            {{ row.status === 'ENABLED' ? '已发布' : '已停用' }}
          </ElTag>
        </template>
      </vxe-grid>
    </div>

    <!-- 分类 -->
    <div class="ml-2 mr-2 w-96">
      <BasicTitle class="mb-1" title="分类" />
      <vxe-grid
        v-bind="categoryOptions"
        ref="categoryRef"
        @cell-click="handleCategoryCellClick"
      />
    </div>

    <!-- 明细 -->
    <div class="w-32 flex-1">
      <BasicTitle class="mb-1" title="明细" />
      <vxe-grid v-bind="detailOptions" ref="detailRef">
        <!-- 安全施工费 -->
        <template #isSafetyConstructionFee-slot="{ row }">
          <VxeCheckbox
            disabled
            v-model="row.isSafetyConstructionFee"
            :checked-value="true"
            :unchecked-value="false"
          />
        </template>
        <!-- 财务成本科目 -->
        <template #financialCostAccountSlot_default="{ row }">
          <span>{{ formateDate(row.financialCostSubjectId) }}</span>
        </template>
      </vxe-grid>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineEmits, defineProps, nextTick, reactive, ref, watch } from 'vue';

import { ElMessage, ElTag } from 'element-plus';
import _ from 'lodash';

import { TreeListFinancialCostAccount } from '#/api/enterpriseCenter/enterpriseStandards/financialCostAccount';
import {
  CategoryList,
  DetailsList,
  VersionList,
} from '#/api/projectCenter/projectSetting/businessCost';
import BasicTitle from '#/components/BasicTitleBar/index.vue';
import { getNamesFromTreeByIds } from '#/utils/common';

const props = withDefaults(
  defineProps<{
    dictType: string;
    dictTypeId: string;
  }>(),
  {
    dictTypeId: '',
    dictType: 'BUSINESS_COST_SUBJECT',
  },
);
const emit = defineEmits<{
  (e: 'componentsEvent', payload: Object): void;
}>();

const selectVersionId = ref(''); // 已选择版本
const isUseVersion = ref(false); // 是否有已使用版本

const currentVersion = ref({});
const versionRef = ref();
const versionOptions = reactive({
  height: 700,
  size: 'mini',
  border: true,
  align: 'center',
  keepSource: true,
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  columnConfig: {
    resizable: true,
  },
  checkboxConfig: {
    showHeader: false,
    checkMethod: ({ row }: { row: any }) => {
      return !isUseVersion.value;
    },
  },
  columns: [
    {
      type: 'checkbox',
      width: 40,
    },
    {
      field: 'name',
      title: '名称',
      showOverflow: 'title',
    },
    {
      field: 'orgName',
      title: '编制单位',
    },
    {
      field: 'status',
      title: '状态',
      slots: {
        default: 'status',
      },
    },
  ],
  data: [],
});
async function handleVersionCellClick({ row }: any) {
  currentVersion.value = row;
  categoryOptions.data = await CategoryList(props.dictTypeId, row.id);
  await nextTick(() => {
    categoryRef.value.setAllTreeExpand(true);
  });
}
function handleVersionCheckBoxChange({ row, records }: any) {
  const selectedVersion = records.filter((item: any) => !item.isUse);
  if (selectedVersion.length > 1) {
    ElMessage.warning('只能选择一个业务版本！');
    nextTick(() => {
      versionRef.value.setCheckboxRow(row, false);
    });
    return;
  }
  selectVersionId.value = row.id;
  emit('componentsEvent', {
    versionId: selectVersionId.value,
  });
}
// 分类
const categoryRef = ref();
const categoryOptions = reactive<any>({
  height: 700,
  size: 'mini',
  border: true,
  align: 'center',
  keepSource: true,
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  columnConfig: {
    resizable: true,
  },
  columns: [
    {
      treeNode: true,
      field: 'code',
      title: '编码',
    },
    {
      field: 'name',
      title: '名称',
    },
    {
      field: 'remark',
      title: '备注',
    },
  ],
  data: [],
});
async function handleCategoryCellClick({ row }: any) {
  detailOptions.data = await DetailsList(
    props.dictTypeId,
    row.versionId,
    row.id,
  );
}
// 明细
const detailRef = ref();
const detailOptions = reactive<any>({
  height: 700,
  size: 'mini',
  border: true,
  align: 'center',
  keepSource: true,
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  columnConfig: {
    resizable: true,
  },
  columns: [
    {
      title: '编码',
      field: 'code',
    },
    {
      width: 150,
      field: 'name',
      title: '业务成本科目名称',
    },
    {
      field: 'unit',
      title: '单位',
    },
    {
      field: 'expenseCategory',
      title: '费用类别',
    },
    {
      field: 'accountingDescription',
      title: '核算说明',
    },
    {
      field: 'isSafetyConstructionFee',
      title: '安全施工费',
      slots: {
        default: 'isSafetyConstructionFee-slot',
      },
    },
    {
      field: 'financialCostSubjectId',
      title: '财务成本科目对照',
      width: 120,
      slots: {
        default: 'financialCostAccountSlot_default',
      },
    },
    {
      field: 'subjectMappingDescription',
      title: '科目对照说明',
    },
  ],
  data: [],
});

// 获取财务成本科目树
const financialCostAccountTreeList = ref([]);
async function getFinancialCostAccount() {
  financialCostAccountTreeList.value = await TreeListFinancialCostAccount();
}
function formateDate(id: string) {
  return getNamesFromTreeByIds(financialCostAccountTreeList.value, [id]);
}
getFinancialCostAccount();

watch(
  () => props.dictType,
  () => {
    getVersionList(props.dictType, props.dictTypeId);
  },
  {
    immediate: true,
  },
);

// 获取分类
async function getVersionList(dictType: string, dictTypeId: string) {
  versionOptions.data = await VersionList(dictTypeId);
  const isSelect = versionOptions.data.filter((item) => item.isUse);
  // 是否有已使用的版本
  isUseVersion.value = !_.isEmpty(isSelect);
  if (isUseVersion.value) {
    await nextTick(() => {
      versionRef.value.setCheckboxRow(isSelect, true);
    });
  }
}
</script>

<style scoped lang="scss"></style>
