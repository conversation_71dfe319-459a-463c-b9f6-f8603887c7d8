.footer {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f2f4f7;
  z-index: 9;
  /* position: fixed;
  bottom: 0;
  left: 0; */
  font-size: 12px;
  padding: 0 4px 0 20px;
  box-sizing: border-box;
}


.footer > div:first-child {
  display: flex;
  align-items: center;
}

.footer .catalog-mode {
  padding: 1px;
  position: relative;
}

.footer .catalog-mode i {
  width: 16px;
  height: 16px;
  margin-right: 5px;
  cursor: pointer;
  display: inline-block;
  background-image: url('../../assets/images/catalog.svg');
}

.footer .page-mode {
  padding: 1px;
  position: relative;
}

.footer .page-mode i {
  width: 16px;
  height: 16px;
  margin-right: 5px;
  cursor: pointer;
  display: inline-block;
  background-image: url('../../assets/images/page-mode.svg');
}

.footer .options {
  width: 70px;
  position: absolute;
  left: 0;
  bottom: 25px;
  padding: 10px;
  background: #fff;
  font-size: 14px;
  box-shadow: 0 2px 12px 0 rgb(56 56 56 / 20%);
  border: 1px solid #e2e6ed;
  border-radius: 2px;
  display: none;
}

.footer .options.visible {
  display: block;
}

.footer .options li {
  padding: 5px;
  margin: 5px 0;
  user-select: none;
  transition: all 0.3s;
  text-align: center;
  cursor: pointer;
}

.footer .options li:hover {
  background-color: #ebecef;
}

.footer .options li.active {
  background-color: #e2e6ed;
}

.footer > div:first-child > span {
  display: inline-block;
  margin-right: 5px;
  letter-spacing: 1px;
}

.footer > div:last-child {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.footer > div:last-child > div {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.footer > div:last-child > div:hover {
  background: rgba(25, 55, 88, 0.04);
}

.footer > div:last-child i {
  width: 16px;
  height: 16px;
  display: inline-block;
  cursor: pointer;
}

.footer .editor-option i {
  background-image: url('../../assets/images/option.svg');
}

.footer .page-scale-minus i {
  background-image: url('../../assets/images/page-scale-minus.svg');
}

.footer .page-scale-add i {
  background-image: url('../../assets/images/page-scale-add.svg');
}

.footer .page-scale-percentage {
  cursor: pointer;
  user-select: none;
}

.footer .fullscreen i {
  background-image: url('../../assets/images/request-fullscreen.svg');
}

.footer .fullscreen.exist i {
  background-image: url('../../assets/images/exit-fullscreen.svg');
}

.footer .paper-margin i {
  background-image: url('../../assets/images/paper-margin.svg');
}

.footer .editor-mode {
  cursor: pointer;
  user-select: none;
}

.footer .paper-size {
  position: relative;
}

.footer .paper-size i {
  background-image: url('../../assets/images/paper-size.svg');
}

.footer .paper-size .options {
  right: 0;
  left: unset;
}

.footer .paper-direction {
  position: relative;
}

.footer .paper-direction i {
  background-image: url('../../assets/images/paper-direction.svg');
}

.footer .paper-direction .options {
  right: 0;
  left: unset;
}
