<template>
  <div
    v-loading="loading"
    class="wpswordeditor h-full w-full"
    ref="wpsRef"
  ></div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';

import { useAccessStore } from '@vben/stores';

import _ from 'lodash';

import { getWpsFileId } from '#/api/couldApi';

defineOptions({
  name: 'WpsWordEditor',
});
const emit = defineEmits<{
  (e: 'controlSelect', row: any): void; // 控件选择
  (e: 'controlRemove', row: any): void; // 控件移除
  (e: 'end'): void;
}>();

// 传入的配置参数
const _options = ref({
  editable: false,
  protectable: false,
});
declare const WebOfficeSDK: any;

const loading = ref(false);
const productCode = 'ecost'; // 产品名称
const wpsAppId = 'SX20250523AKCYPY'; // appId
const fileId = ref(''); // 文件Id
let instance: any = null; // 文件 Instance
const controls = ref<any>([]); // 所有控件
const wpsRef = ref(); // wpsEl

// 获取所有的控件
async function getAllControl() {
  await instance.ready();
  const app = instance.Application;
  const contentControls = await app.ActiveDocument.ContentControls;
  const count = await contentControls.Count;

  const allControls = [];
  for (let i = 1; i <= count; i++) {
    const control = await contentControls.Item(i);
    allControls.push(control);
  }
  return allControls;
}
// 获取所有的控件的唯一标识
async function getAllControlIds() {
  await instance.ready();
  const app = instance.Application;
  const contentControls = await app.ActiveDocument.ContentControls;
  const count = await contentControls.Count;
  const ids: string[] = [];
  for (let i = 1; i <= count; i++) {
    const control = await contentControls.Item(i);
    const Tag = await control.Tag;
    ids.push(Tag);
  }
  return ids;
}

// 获取所有控件的标识
const controlTags = ref<any>([]);
watch(controlTags, async (nval, oval) => {
  const deleted = _.uniq(oval).filter((val) => !nval.includes(val));
  if (deleted.length > 0) {
    const deletedItem = deleted[0];
    emit('controlRemove', deletedItem);
  }
});

// 插入控件
// isBreak 是否需要换行
async function setControl(mark: string, isBreak = false) {
  const app = instance.Application;
  if (isBreak) {
    await app.ActiveDocument.ActiveWindow.Selection.InsertParagraph();
    await app.ActiveDocument.ActiveWindow.Selection.MoveUp();
  }
  const contentControls = await app.ActiveDocument.ContentControls;
  const contentControl = await contentControls.Add({
    Type: 0,
  });
  await contentControl.SetPlaceholderText({ Text: mark });
  contentControl.Tag = mark;

  if (isBreak) {
    await app.ActiveDocument.ActiveWindow.Selection.MoveDown();
  }
}

// 选择区变化
async function selectionChange(data: any) {
  const controls = data.ctrls;
  if (controls.length > 0) {
    const control = controls[0];
    const tag = await control.tag;
    emit('controlSelect', tag);
  }
}

// 插入表格
async function insertTable(contentControl: any, data: any) {
  loading.value = true;
  try {
    const { header, body, options } = data;
    const headerData = header || [];
    const bodyData = body || [];

    const { rows = 1, cols = 1 } = options;
    const app = instance.Application;
    // 获取内容控件的范围
    const controlRange = await contentControl.Range;
    const controlStart = await controlRange.Start;
    const controlEnd = await controlRange.End;
    await app.ActiveDocument.Range.SetRange(controlStart, controlEnd);
    // 获取所有表格
    const tables = await app.ActiveDocument.Tables;
    // 删除控件范围内已有的表格
    const tableCount = await tables.Count;
    for (let i = tableCount; i >= 1; i--) {
      const table = await tables.Item(i);
      const tableRange = await table.Range;
      const tableStart = await tableRange.Start;
      const tableEnd = await tableRange.End;
      // 判断表格是否在控件范围内
      if (tableStart >= controlStart && tableEnd <= controlEnd) {
        await table.Delete();
      }
    }
    // 插入的表格
    const table = await tables.Add(
      app.ActiveDocument.ActiveWindow.Selection.Range, // 位置信息
      rows, // 新增表格的行数
      cols, // 新增表格的列数
      1, // 启用自动调整功能
      1, // 根据表格中包含的内容自动调整表格的大小
    );
    const tableRange = await table.Range;
    tableRange.ParagraphFormat.SetAlignment(app.Enum.WdAlignmentMode.wdCenter);
    // 插入表头
    for (let index = 0; index < cols; index++) {
      const cell = await table.Rows.Item(1).Cells.Item(index + 1);
      // 获取该单元格的区域
      const textRange = await cell.Range;
      textRange.Text = headerData[index];
    }
    // 插入内容
    for (const [i, rowData] of bodyData.entries()) {
      for (const [j, text] of rowData.entries()) {
        // 排除标题从第二行开始
        const cell = await table.Rows.Item(i + 2).Cells.Item(j + 1);
        const textRange = await cell.Range;
        textRange.Text = text;
      }
    }

    loading.value = false;
  } catch {
    loading.value = false;
  }
}

// 数据
async function findControlsByTag(mark: string) {
  const results = await Promise.all(
    controls.value.map(async (item: any) => {
      const tag = await item.Tag;
      return { item, match: tag === mark };
    }),
  );

  const matchedItems = results
    .filter((result) => result.match)
    .map((result) => result.item);

  return matchedItems;
}
// 替换数据
async function replaceControlData(mark: string, type: string, data: any) {
  // 多个数据替换
  const controlsData = await findControlsByTag(mark);
  if (type === 'text') {
    for (const control of controlsData) {
      await control.SetPlaceholderText({ Text: data });
    }
  } else if (type === 'table') {
    for (const control of controlsData) {
      insertTable(control, data);
    }
  }
}

const accessStore = useAccessStore();
// 是否不需要主动刷新token的? 系统token无感刷新后为最新
const refreshToken = () => {
  const token = accessStore.accessToken;
  return new Promise((resolve, reject) => {
    // 异步操作
    setTimeout(() => {
      const success = true;
      if (success) {
        resolve({
          token,
          timeout: 10 * 60 * 1000,
        });
      } else {
        reject(new Error('刷新失败'));
      }
    }, 0);
  });
};
// 初始化编辑器
const initEditor = async () => {
  const token = accessStore.accessToken;
  const mount = wpsRef.value;
  instance = WebOfficeSDK.init({
    officeType: WebOfficeSDK.OfficeType.Writer,
    appId: wpsAppId,
    fileId: fileId.value,
    mount,
    token,
    refreshToken,
    commandBars: [
      {
        cmbId: 'TabInsertTab', // 工具栏-插入按钮
        attributes: {
          visible: false, // 隐藏组件
          enable: false, // 禁用组件，组件显示但不响应点击事件
        },
      },
      {
        cmbId: 'TabReviewWord', // 工具栏-审阅按钮
        attributes: {
          visible: false,
          enable: false,
        },
      },
      {
        cmbId: 'TabPageTab', // 工具栏-页面按钮
        attributes: {
          visible: false,
          enable: false,
        },
      },
      {
        cmbId: 'ContextMenuConvene', // 右键-召唤在线协助者
        attributes: {
          visible: false,
          enable: false,
        },
      },
      {
        cmbId: 'BookMarkContextMenu', // 文字-右键-插入书签
        attributes: {
          visible: false,
          enable: false,
        },
      },
      {
        cmbId: 'TabViewTab', // 工具栏-视图 Tab
        attributes: {
          visible: false, // 隐藏组件
          enable: false, // 禁用组件，组件显示但不响应点击事件
        },
      },
      {
        cmbId: 'ViewTabTemplate', // 工具栏-母版视图
        attributes: {
          visible: false, // 隐藏组件
          enable: false, // 禁用组件，组件显示但不响应点击事件
        },
      },
    ],
  });
  await instance.ready();

  await new Promise((resolve) => setTimeout(resolve, 500));
  const app = instance.Application;
  // 获取修订对象
  const revisions = await app.ActiveDocument.Revisions;
  // 设置修订框显示
  revisions.ShowRevisionsFrame = false;

  // // 将当前文档的编辑状态切换成编辑模式
  app.ActiveDocument.TrackRevisions = false;
  // 是否在正文中显示评论
  app.ActiveDocument.ActiveWindow.View.ShowComments = false;

  // 监听选取变化事件
  const handleContentChange = _.debounce(async (data) => {
    controls.value = await getAllControl();
    controlTags.value = await getAllControlIds();
    selectionChange(data);
  }, 400); // 350ms内只执行最后一次

  instance.ApiEvent.AddApiEventListener(
    'WindowSelectionChange',
    handleContentChange,
  );
  // 检查是否已保护
  const mode = await app.ActiveDocument.RestrictEditMode;
  if (!_options.value.editable) {
    try {
      await app.ActiveDocument.SetReadOnly({
        Value: true,
      });
    } catch (error) {
      console.error('文档设置只读失败:', error);
    }
  } else if (mode === 0 && _options.value.protectable) {
    const password = import.meta.env.VITE_DOC_PASSWORD;
    try {
      await app.ActiveDocument.Protect(password);
    } catch (error) {
      console.error('文档保护失败:', error);
    }
  }

  emit('end');
};

export type initType = {
  createTime: number;
  fileContentType: string;
  fileKey: string;
  fileName: string;

  fileSize: number;
  updateTime: number;
};
async function init({ params, options }: { options: any; params: initType }) {
  const {
    fileKey,
    fileName,
    fileContentType,
    fileSize,
    createTime,
    updateTime,
  } = params;
  const { editable, protectable } = options;
  _options.value = {
    editable,
    protectable,
  };
  const res = await getWpsFileId({
    productCode,
    fileKey,
    name: fileName,
    fileContentType,
    size: fileSize,
    create_time: createTime,
    modify_time: updateTime,
  });
  fileId.value = res.fileId;
  initEditor();
}

// 暴露初始化方法
defineExpose({
  init,
  replaceControlData,
  getAllControl,
  setControl,
});
</script>
