<!-- BaseGrid.vue -->
<template>
  <vxe-grid v-bind="mergedOptions" v-on="gridEvents" ref="gridRef">
    <slot name="top"></slot>
    <slot></slot>
  </vxe-grid>
</template>

<script setup lang="ts">
import { computed, reactive } from 'vue';

const props = defineProps({
  tableOptions: Object,
  gridEvents: Object,
});

const defaultOptions = reactive<any>({
  size: 'mini',
  height: '100%',
  border: true,
  align: 'center',
  showOverflow: true,
  keepSource: true,
  headerRowClassName: 'bg-sky-200 text-black-800',
  loading: true,
  columnConfig: {
    resizable: true,
  },
  mouseConfig: {
    selected: true,
  },
  rowConfig: {
    isCurrent: true,
  },
  rowClassName: ({ row }: any) => {
    return row.disabled ? 'bg-gray-100' : '';
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    showIcon: true,
    beforeEditMethod({ row, column }: any) {
      // 版本数据已启用无法进行编辑
      return true;
    },
  },
  menuConfig: {
    // body: {
    //   options: [
    //     [
    //       {
    //         code: 'MOVE_UP',
    //         name: '上移',
    //         prefixConfig: { icon: 'vxe-icon-arrows-up' },
    //         disabled: false,
    //       },
    //       {
    //         code: 'MOVE_DOWN',
    //         name: '下移',
    //         prefixConfig: { icon: 'vxe-icon-arrows-down' },
    //         disabled: false,
    //       },
    //       {
    //         code: 'DELETE_ROW',
    //         name: '删除',
    //         prefixConfig: { icon: 'vxe-icon-delete-fill' },
    //         disabled: false,
    //       },
    //     ],
    //   ],
    // },
    visibleMethod: ({ options, row, rowIndex }: any) => {
      // 内置节点控制
      if (!row?.id || row.id === '') {
        options[0].forEach((item: any) => {
          item.disabled = true;
        });
        return true;
      }
      options[0].forEach((item: any) => {
        switch (item.code) {
          case 'MOVE_DOWN': {
            break;
          }
          case 'MOVE_UP': {
            // item.disabled = rowIndex == 1;
            break;
          }
          // 其他按钮始终可用
          default: {
            item.disabled = false;
          }
        }
      });
      return true;
    },
  },
  columns: [],
  data: [],
  // ...其他默认配置
});

const mergedOptions = computed(() => ({
  ...defaultOptions,
  ...props.tableOptions,
}));
</script>
