import type { IEditorOption, IElement } from '@hufe921/canvas-editor';
import {
  ControlType,
  ElementType,
  ListType,
  TitleLevel
} from '@hufe921/canvas-editor';
interface IComment {
  id: string;
  content: string;
  userName: string;
  rangeText: string;
  createdDate: string;
}
export class TextElementBuilder {
  private text: string = "";
  private titleMap = new Map<number, string>();
  private colorIndex: number[] = [];
  private highlightIndex: number[] = [];
  private elementList: IElement[] = [];
  constructor(contentText: string,{titleText,colorText,highlightText}:{titleText:string[],colorText:string[],highlightText:string[]}) {
    this.text = contentText;

    this.computeTitleMap(titleText);

    this.computeColorIndex(colorText);
    this.computeHighlightIndex(highlightText);
    this.buildElementList();
  }
  // 构建ElementList
  public buildElementList(){
    const list: IElement[] = [];
    const chars = this.text.split('');
    let i = 0;
    while (i < chars.length) {
      const title = this.titleMap.get(i);
      if (title) {
        list.push({
          type: ElementType.TITLE,
          level: TitleLevel.FIRST,
          value: title,
          valueList: [{ value: title, size: 18 }],
        });
        i += title.length;
        continue;
      }
      
      const char = chars[i];
      if (this.colorIndex.includes(i)) {
        list.push({ value: char || '', color: '#FF0000', size: 16 });
      } else if (this.highlightIndex.includes(i)) {
        list.push({ value: char || '', highlight: '#F2F27F', groupIds: ['1'] });
      } else {
        list.push({ value: char || '', size: 16 });
      }

      i++;
    }
    this.elementList = list
  }
  // 计算标题Map
  public computeTitleMap(titleText:string[]) {
    this.titleMap.clear();
    for (const title of titleText) {
      const index = this.text.indexOf(title);
      if (~index) {
        this.titleMap.set(index, title);
      }
    }
  }
  // 计算文字颜色设置
  public computeColorIndex(colorText:string[]) {
    this.colorIndex = colorText
      .map(t => {
        const i = this.text.indexOf(t);
        return ~i ? Array.from({ length: t.length }, (_, j) => i + j) : [];
      })
      .flat();
    console.log(colorText,this.colorIndex)
  }
  // 计算文字高亮设置
  public computeHighlightIndex(highlightText:string[]) {
    this.highlightIndex = highlightText
      .map(t => {
        const i = this.text.indexOf(t);
        return ~i ? Array.from({ length: t.length }, (_, j) => i + j) : [];
      })
      .flat();
  }

  // 获取ElementList
  public getElementList(){
    return this.elementList
  }
  // 设置文本输入控件
  public insertTextInputControl(position = 1,textTips:string) {
    this.elementList.splice(position, 0, {
      type: ElementType.CONTROL,
      value: '',
      control: {
        conceptId: '1',
        type: ControlType.TEXT,
        value: null,
        placeholder: textTips,
        prefix: ' [',
        postfix: '] ',
      }
    });
  }
  // 设置文本选择器控件
  public insertSelectControl(position = 94,valueSets=[]) {
    this.elementList.splice(position, 0, {
      type: ElementType.CONTROL,
      value: '',
      control: {
        conceptId: '2',
        type: ControlType.SELECT,
        value: null,
        placeholder: '有无',
        prefix: '{',
        postfix: '}',
        valueSets: valueSets
      }
    });
  }
  // 设置超链接
  public insertSelectControlHyperlink(position = 116){
    this.elementList.splice(position, 0, {
      type: ElementType.HYPERLINK,
      value: '',
      valueList: [
        {
          value: '新',
          size: 16,
        },
        {
          value: '冠',
          size: 16,
        },
        {
          value: '肺',
          size: 16,
        },
        {
          value: '炎',
          size: 16,
        },
      ],
      url: 'https://hufe.club/canvas-editor',
    });
  }

  // 新增：模拟文本控件（前后文本）
  public insertTextControlWithPrefixAndPostfix(position = 335) {
    this.elementList.splice(position, 0, {
      type: ElementType.CONTROL,
      value: '',
      control: {
        conceptId: '6',
        type: ControlType.TEXT,
        value: null,
        placeholder: '内容',
        preText: '其他：',
        postText: '。',
      },
    });
  }
  // 新增：下标
  public insertSubscript(position = 346) {
    this.elementList.splice(position, 0, {
      value: '∆',
      color: '#FF0000',
      type: ElementType.SUBSCRIPT,
    });
  }
  // 新增：上标
  public insertSuperscript(position = 430) {
    this.elementList.splice(position, 0, {
      value: '9',
      type: ElementType.SUPERSCRIPT,
    });
  }

  // 新增：有序列表
  public insertOrderedList(position: number, text: string) {
    this.elementList.splice(position, 0, {
      value: '',
      type: ElementType.LIST,
      listType: ListType.OL,
      valueList: [
        {
          value: text,
        },
      ],
    });
  }

  // 新增：图片
  public insertImage(position: number, base64: string) {
    this.elementList.splice(position, 0, {
      value: base64,
      type: ElementType.IMAGE,
    });
  }

  // 插入表格（默认 2x2 表格，可根据需要扩展）
  public insertTable(position = 460, rows = 2, cols = 2) {
    this.elementList.splice(position, 0, {
      type: ElementType.TABLE,
      value: '',
      colgroup: [
        {
          width: 180,
        },
        {
          width: 80,
        },
        {
          width: 130,
        },
        {
          width: 130,
        },
      ],
      trList: [
        {
          height: 40,
          tdList: [
            {
              colspan: 1,
              rowspan: 2,
              value: [
                { value: `1`, size: 16 },
                { value: '.', size: 16 },
              ],
            },
            {
              colspan: 1,
              rowspan: 1,
              value: [
                { value: `2`, size: 16 },
                { value: '.', size: 16 },
              ],
            },
            {
              colspan: 2,
              rowspan: 1,
              value: [
                { value: `3`, size: 16 },
                { value: '.', size: 16 },
              ],
            },
          ],
        },
        {
          height: 40,
          tdList: [
            {
              colspan: 1,
              rowspan: 1,
              value: [
                { value: `4`, size: 16 },
                { value: '.', size: 16 },
              ],
            },
            {
              colspan: 1,
              rowspan: 1,
              value: [
                { value: `5`, size: 16 },
                { value: '.', size: 16 },
              ],
            },
            {
              colspan: 1,
              rowspan: 1,
              value: [
                { value: `6`, size: 16 },
                { value: '.', size: 16 },
              ],
            },
          ],
        },
        {
          height: 40,
          tdList: [
            {
              colspan: 1,
              rowspan: 1,
              value: [
                { value: `7`, size: 16 },
                { value: '.', size: 16 },
              ],
            },
            {
              colspan: 1,
              rowspan: 1,
              value: [
                { value: `8`, size: 16 },
                { value: '.', size: 16 },
              ],
            },
            {
              colspan: 1,
              rowspan: 1,
              value: [
                { value: `9`, size: 16 },
                { value: '.', size: 16 },
              ],
            },
            {
              colspan: 1,
              rowspan: 1,
              value: [
                { value: `1`, size: 16 },
                { value: `0`, size: 16 },
                { value: '.', size: 16 },
              ],
            },
          ],
        },
      ],
    });
  }

  // 插入复选框控件
  public insertCheckbox(position = 470, placeholder = '选项') {
    this.elementList.push(
      ...(<IElement[]>[
        {
          value: '是否同意以上内容：',
        },
        {
          type: ElementType.CONTROL,
          control: {
            conceptId: '3',
            type: ControlType.CHECKBOX,
            code: '98175',
            value: '',
            valueSets: [
              {
                value: '同意',
                code: '98175',
              },
              {
                value: '否定',
                code: '98176',
              },
            ],
          },
          value: '',
        },
        {
          value: '\n',
        },
      ]),
    );
  }

  // 插入 LaTeX 公式控件
  public insertLatex(position = 480, latex = 'e^{i\\pi} + 1 = 0') {
    this.elementList.push(
      ...(<IElement[]>[
        {
          value: '医学公式：',
        },
        {
          value: `{E_k} = hv - {W_0}`,
          type: ElementType.LATEX,
        },
        {
          value: '\n',
        },
      ]),
    );
  }

  // 插入日期选择器控件
  public insertDatePicker(position = 490) {
    this.elementList.push(
      ...(<IElement[]>[
        {
          value: '签署日期：',
        },
        {
          type: ElementType.CONTROL,
          value: '',
          control: {
            conceptId: '5',
            type: ControlType.DATE,
            value: [
              {
                value: `2022-08-10 17:30:01`,
              },
            ],
            placeholder: '签署日期',
          },
        },
        {
          value: '\n',
        },
      ]),
    );
  }

  // 插入固定长度下划线控件
  public insertFixedLengthUnderline(position = 500, length = 100) {
    this.elementList.push(
      ...[
        {
          value: '患者签名：',
        },
        {
          type: ElementType.CONTROL,
          value: '',
          control: {
            conceptId: '4',
            type: ControlType.TEXT,
            value: null,
            placeholder: '',
            prefix: '\u200c',
            postfix: '\u200c',
            minWidth: 160,
            underline: true,
          },
        },
      ],
    );
  }

  // 设置结尾文本
  public insertEndText(position = 335, placeholder = '内容') {
    this.elementList.push(
      ...[
        {
          value: '\n',
        },
        {
          value: '',
          type: ElementType.TAB,
        },
        {
          value: 'E',
          size: 16,
        },
        {
          value: 'O',
          size: 16,
        },
        {
          value: 'F',
          size: 16,
        },
      ],
    );
  }


  public getBindItem(){
    const commentList: IComment[] = [
      {
        id: '1',
        content:
          '红细胞比容（HCT）是指每单位容积中红细胞所占全血容积的比值，用于反映红细胞和血浆的比例。',
        userName: 'Hufe',
        rangeText: '血细胞比容',
        createdDate: '2023-08-20 23:10:55',
      },
    ];
    return commentList
  }

  public getOption(watermark = 'E-Wing'){
    return {
      margins: [100, 120, 100, 120],
      watermark: {
        data: watermark,
        size: 120,
      },
      pageNumber: {
        format: '第{pageNo}页/共{pageCount}页',
      },
      placeholder: {
        data: '请输入正文',
      },
      zone: {
        tipDisabled: false,
      },
      maskMargin: [60, 0, 30, 0], // 菜单栏高度60，底部工具栏30为遮盖层
    } as IEditorOption;
  }
}