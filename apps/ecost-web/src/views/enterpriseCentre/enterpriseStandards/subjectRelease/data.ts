import { ref } from 'vue';

/**
 * 分列组件配置
 */
export const colPageProps = ref({
  leftCollapsedWidth: 3,
  leftCollapsible: true,
  leftMaxWidth: 25,
  leftMinWidth: 20,
  leftWidth: 25,
  resizable: true,
  rightWidth: 75,
  splitHandle: true,
  splitLine: true,
  autoContentHeight: true,
});

// 企业标准版本启用状态
export enum EnableStatus {
  DISABLED = 'DISABLED', //  已停用
  ENABLED = 'ENABLED', //  已启用
  NOT_ENABLED = 'NOT_ENABLED', //  未启用
}
// 企业标准版本启用状态TEXT
export enum EnableStatusText {
  DISABLED = '已停用',
  ENABLED = '已启用',
  NOT_ENABLED = '未启用',
}
