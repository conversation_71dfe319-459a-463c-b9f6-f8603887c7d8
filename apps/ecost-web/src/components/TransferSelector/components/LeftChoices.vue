<template>
  <div class="selections h-full">
    <div class="flex h-[30px] justify-between text-[14px]">
      <ElRadioGroup v-model="currentTab">
        <ElRadio value="1" size="small">选择材料字典</ElRadio>
        <ElRadio value="2" size="small" disabled>选择材料总计划</ElRadio>
      </ElRadioGroup>
    </div>
    <div class="flex h-[calc(100%-30px)] w-full justify-between">
      <!-- 分类 -->
      <div class="h-full w-[48%]">
        <div class="item-center mb-[8px] flex h-[30px] gap-6">
          <ElInput placeholder="搜索材料明细" v-model="filterText" size="small">
            <template #prefix>
              <IconifyIcon icon="ep:search" />
            </template>
          </ElInput>
          <TreeLevelExpand
            :expand-idx="expandIdx"
            @expand-click="expandClick"
          />
        </div>
        <div class="h-[calc(100%-38px)]">
          <vxe-grid
            ref="prevTableRef"
            v-bind="prevTableOptions"
            v-on="prevGridEvents"
          >
            <template #top> </template>
            <template #seq="{ $rowIndex }">
              <div>{{ $rowIndex + 1 }}</div>
            </template>
            <template #type="{ row }">
              <div>{{ getMATERIALStatusLabel(row.type) }}</div>
            </template>
          </vxe-grid>
        </div>
      </div>
      <!-- 明细 -->
      <div class="h-full w-[48%]">
        <div class="item-center mb-[8px] flex h-[30px]">
          <ElInput placeholder="搜索材料明细" v-model="filterText" size="small">
            <template #prefix>
              <IconifyIcon icon="ep:search" />
            </template>
          </ElInput>
        </div>

        <div class="h-[calc(100%-38px)]">
          <vxe-grid ref="tableRef" v-bind="tableOptions" v-on="gridEvents">
            <template #seq="{ $rowIndex }">
              <div>{{ $rowIndex + 1 }}</div>
            </template>
            <template #type="{ row }">
              <div>{{ getMATERIALStatusLabel(row.type) }}</div>
            </template>
          </vxe-grid>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, reactive, ref, watch } from 'vue';

import { IconifyIcon } from '@vben/icons';

import { ElInput, ElRadio, ElRadioGroup } from 'element-plus';

import TreeLevelExpand from '#/components/TreeLevelExpand/index.vue';
import { flattenTreeToLevel } from '#/utils/vxeTool';

defineOptions({
  name: 'Choices',
});

const props = withDefaults(
  defineProps<{
    choiceClassData: any[];
    choiceDetailData: any[];
    selectionData: any[];
  }>(),
  {
    choiceClassData: () => [],
    choiceDetailData: () => [],
    selectionData: () => [],
  },
);
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'select', data: any): void;
  (e: 'change', data: any): void;
}>();

// 审核状态
const MATERIALStatus = {
  CONSUME_MATERIAL: 'CONSUME_MATERIAL', // 待审核
  CONCRETE: 'CONCRETE', // 审批中
  TURNOVERME_MATERIAL: 'TURNOVERME_MATERIAL', // 审核通过
  FIXEDASSETSL_CONSUMABLES: 'FIXEDASSETSL_CONSUMABLES', // 审核拒绝
} as const;
type MATERIALStatusType = (typeof MATERIALStatus)[keyof typeof MATERIALStatus];
function getMATERIALStatusLabel(status: MATERIALStatusType) {
  const map = {
    [MATERIALStatus.CONSUME_MATERIAL]: '消耗材料',
    [MATERIALStatus.CONCRETE]: '商品混凝土',
    [MATERIALStatus.TURNOVERME_MATERIAL]: '周转材料',
    [MATERIALStatus.FIXEDASSETSL_CONSUMABLES]: '固定资产/低值易耗品',
  };
  return map[status] || '';
}

// tab项
const currentTab = ref('1');
// 展开层级下标
const expandIdx = ref(0);
// 筛选数据
const filterText = ref('');
// 当前选中的分类表格数据
const currentClassItem = ref({
  id: '',
});
// 分类表格数据
const prevTableRef = ref();
// 内置分类数据
const staticClassItem = {
  id: '',
  name: '全部',
  parentId: null,
  remark: '',
  type: '',
  disabled: true,
};
// 分类表格配置
const prevColumns = [
  {
    field: 'code',
    title: '编码',
    width: '80',
    treeNode: true,
  },
  {
    field: 'name',
    title: '类别名称',
    minWidth: '100',
  },
  {
    field: 'type',
    title: '核算类型',
    width: '100',
    slots: {
      default: 'type',
    },
  },
  {
    field: 'remark',
    title: '备注',
    width: '80',
  },
];
const prevTableOptions = reactive<any>({
  size: 'mini',
  height: '100%',
  border: true,
  align: 'center',
  showOverflow: true,
  keepSource: true,
  headerRowClassName: 'bg-[skyblue]',
  rowClassName: ({ row }: any) => {
    return row.disabled ? 'bg-gray-100' : '';
  },
  loading: false,
  columnConfig: {
    resizable: true,
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  rowConfig: {
    isCurrent: true,
  },
  columns: prevColumns,
  data: [],
});
watch(
  () => props.choiceClassData,
  (nval) => {
    prevTableOptions.data = [staticClassItem, ...nval];
  },
  { immediate: true },
);
// 表格事件
const prevGridEvents = {
  cellClick({ row }: { row: any }) {
    if (row.id === currentClassItem.value.id) return;
    currentClassItem.value = row;
    emit('select', row);
  },
};
// 明细表格数据
const tableRef = ref();
// 明细表格配置
const columns = [
  {
    file: 'seq',
    title: '',
    width: '60',

    slots: {
      default: 'seq',
    },
  },
  {
    field: 'code',
    title: '编码',
    width: '80',
  },
  {
    field: 'name',
    title: '名称',
    minWidth: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
    },
  },
  {
    field: 'specificationModel',
    title: '规格型号',
    width: '80',
  },
  {
    field: 'type',
    title: '核算类型',
    width: '100',
    slots: {
      default: 'type',
    },
  },
  {
    field: 'meteringUnit',
    title: '计量单位',
    width: '80',
  },
  {
    field: 'remark',
    title: '备注',
    width: '80',
  },
];
const tableOptions = reactive<any>({
  size: 'mini',
  height: '100%',
  border: true,
  align: 'center',
  showOverflow: true,
  keepSource: true,
  headerRowClassName: 'bg-[skyblue]',
  loading: false,
  rowClassName: ({ row }: any) => {
    return row.selected ? 'bg-orange-100' : '';
  },
  columnConfig: {
    resizable: true,
  },
  mouseConfig: {
    selected: true,
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  rowConfig: {
    isCurrent: true,
  },
  columns,
  data: [],
});
// 表格数据赋值
watch(
  () => props.choiceDetailData,
  (nval) => {
    tableOptions.data = nval;
  },
  {
    immediate: true,
  },
);

// 表格数据状态修改
const selections = ref(props.selectionData);
watch(
  () => props.selectionData,
  (nval) => {
    selections.value = nval;
    const ids = new Set(selections.value.map((item) => item.id));
    tableOptions.data.forEach((item: any) => {
      const id = item.id;
      const selected = !!ids.has(id);
      item.selected = selected;
    });
  },
  {
    immediate: true,
  },
);

// 明细表格事件
const gridEvents = {
  cellDblclick({ row }: any) {
    if (!row.selected) {
      emit('change', row);
    }
  },
};

const expandClick = (level: number) => {
  expandIdx.value = level;
  const $grid = prevTableRef.value;
  $grid.clearTreeExpand();
  if (level === 0) {
    $grid.setAllTreeExpand(true);
  } else {
    const tableData = $grid.getTableData();
    const targetData = flattenTreeToLevel(tableData.fullData, level);
    $grid.setTreeExpand(targetData, true);
  }
};

function init() {
  if (prevTableOptions.data.length > 0) {
    const currentRow = prevTableOptions.data[0];
    emit('select', currentRow);
    setCurrentRow(currentRow.id);
  }
}

async function setCurrentRow(
  value: string,
  key: string = 'id',
  isExpand: boolean = true,
) {
  const activeRow = prevTableOptions.data.find((v: any) => v[key] === value);
  const $grid = prevTableRef.value;
  nextTick(() => {
    if (activeRow) {
      $grid.setCurrentRow(activeRow);
      currentClassItem.value = activeRow;
    }
    $grid.setAllTreeExpand(isExpand);
  });
}

onMounted(() => {
  init();
});
</script>

<style scoped lang="scss">
.el-radio {
  margin-right: 20px;
}
</style>
