# 📄 WpsWordEditor 使用说明文档

## 🧩 组件名称

`WpsWordEditor`

## 📦 基本用途

用于在 Vue 项目中集成 WPS 在线文档编辑功能，可插入文字、表格、控件等内容，并支持通过标识替换控件内容。

---

## 🛠 初始化方法

组件通过 `init()` 方法完成初始化。

### 🔧 方法签名

```ts
init({
  options: {
    editable: boolean,
    protectable: boolean,
  },
  params: {
    fileKey: string,
    fileName: string,
    fileContentType: string,
    fileSize: number,
    createTime: number,
    updateTime: number,
  },
}): void
```

### 📥 参数说明

#### options

| 参数            | 类型      | 说明                  |
| ------------- | ------- | ------------------- |
| `editable`    | boolean | 是否只读（true 为只读）      |
| `protectable` | boolean | 是否启用文档保护（只允许指定控件编辑） |

#### params

| 参数                | 类型     | 说明                                                                               |
| ----------------- | ------ | -------------------------------------------------------------------------------- |
| `fileKey`         | string | 文件唯一标识，用于后端生成 fileId                                                             |
| `fileName`        | string | 文件名称                                                                             |
| `fileContentType` | string | 文件类型，如 `application/vnd.openxmlformats-officedocument.wordprocessingml.document` |
| `fileSize`        | number | 文件大小（单位 Byte）                                                                    |
| `createTime`      | number | 创建时间（时间戳）                                                                        |
| `updateTime`      | number | 修改时间（时间戳）                                                                        |

---

## 🧵 暴露方法

组件通过 `defineExpose` 暴露以下方法供父组件调用：

### 1. `init()`

初始化编辑器（见上文）。

### 2. `replaceControlData(mark: string, type: 'text' | 'table', data: any)`

根据控件标识 `mark` 替换控件中的内容。

* **mark**: 控件唯一标识（Tag）
* **type**: 内容类型，`text` 表示纯文字，`table` 表示插入表格
* **data**: 插入的内容，如果是 `text` 传字符串，如果是 `table` 可传表格数据

使用示例：

```ts
editorRef.value.replaceControlData('projectTable', 'table', tableData);
```

---

## 📣 事件（Emits）

组件通过 `emit` 向父组件传递以下事件：

| 事件名             | 参数         | 说明                     |
| --------------- | ---------- | ---------------------- |
| `controlSelect` | `row: any` | 当选中某个控件时触发，返回控件标识（Tag） |
| `controlRemove` | `row: any` | 当控件被删除时触发，返回被删除的控件 tag |
| `end`           | 无          | 初始化完成后触发               |

---

## ✅ 使用示例

```ts
const editorRef = ref();

editorRef.value.init({
  options: {
    editable: true,
    protectable: false,
  },
  params: {
    fileKey: 'abc123',
    fileName: '测试文档.docx',
    fileContentType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    fileSize: 102400,
    createTime: Date.now(),
    updateTime: Date.now(),
  }
});

editorRef.value.replaceControlData('myTag', 'text', '插入的文字内容');
```

```vue
<WpsWordEditor
  ref="editorRef"
  @controlSelect="onControlSelected"
  @controlRemove="onControlRemoved"
  @end="onEditorReady"
/>
```

---

如需更多功能（如控制工具栏显示、表格样式自定义、控件操作等），可查阅 WebOfficeSDK 官方文档或联系组件维护者。
