<template>
  <Page auto-content-height>
    <div class="bg-card flex h-full flex-col rounded-lg border p-2">
      <div class="mb-2 flex items-center justify-between">
        <ElButton type="primary" size="small" @click="showDrawer = true">
          新增
        </ElButton>
        <ElDropdown size="small" @command="handleDropDownItem">
          <ElButton plain type="primary" size="small">
            导入<IconifyIcon icon="bi:arrow-down" />
          </ElButton>
          <template #dropdown>
            <ElDropdownMenu>
              <ElDropdownItem command="download"> 模版下载 </ElDropdownItem>
            </ElDropdownMenu>
          </template>
        </ElDropdown>
      </div>
      <div class="flex-1 overflow-hidden">
        <vxe-grid v-bind="supplierOptions" ref="supplierOptionsRef">
          <template #name_slot="{ row }">
            <ElLink type="primary" @click="jumpDrawer(row)">
              {{ row.fullName }}
            </ElLink>
          </template>
          <!-- 供应商分类 -->
          <template #classify_slot="{ row }">
            {{ supplierLabel(row.classify) }}
          </template>
          <!-- 纳税人类型 -->
          <template #taxpayerQualification_slot="{ row }">
            {{ taxpayerLabel(row.taxpayerQualification) }}
          </template>
        </vxe-grid>
      </div>
      <AddOrEdit
        v-model:visible="showDrawer"
        :row-id="rowId"
        @refresh="drawerRefresh"
      />
    </div>
  </Page>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import {
  ElButton,
  ElDropdown,
  ElDropdownItem,
  ElDropdownMenu,
  ElLink,
} from 'element-plus';

import { ListSupplier } from '#/api/enterpriseCenter/enterpriseStandards/supplier';
import { downloadLocalFile } from '#/utils';

import AddOrEdit from './components/AddorEdit.vue';
import { supplierCategory, taxpayerType } from './data';

const rowId = ref('');
const showDrawer = ref(false);
const supplierOptionsRef = ref();
const supplierOptions = reactive<any>({
  size: 'mini',
  height: '100%',
  autoresize: true,
  border: true,
  keepSource: true,
  align: 'center',
  showOverflow: true,
  columnConfig: {
    resizable: true,
  },
  rowDragConfig: {
    isCrossDrag: true,
  },
  rowConfig: {
    isCurrent: true,
    // isHover: true,
  },
  headerRowStyle: () => {
    return {
      backgroundColor: '#b3daf1',
      color: '#333',
    };
  },
  columns: [
    { type: 'seq', width: 70, title: '序号', fixed: 'left' },
    {
      field: 'fullName',
      title: '供应商全称',
      width: 200,
      fixed: 'left',
      slots: {
        default: 'name_slot',
      },
      filters: [{ data: '' }],
      filterMethod({
        option,
        row,
        column,
      }: {
        column: any;
        option: any;
        row: any;
      }) {
        if (option.data) {
          return `${row[column.field]}`.includes(option.data);
        }
        return true;
      },
      filterRender: {
        name: 'FilterInput',
      },
    },
    // {
    //   field: 'introductionAt',
    //   title: '引进时间',
    //   width: 150,
    // },
    // {
    //   field: '',
    //   title: '评价时间',
    //   width: 150,
    // },
    // {
    //   field: '',
    //   title: '供应商评级',
    //   width: 150,
    // },
    // {
    //   field: '',
    //   title: '退场时间',
    //   width: 150,
    // },
    // {
    //   field: '',
    //   title: '淘汰时间',
    //   width: 150,
    // },
    {
      field: 'simpleName',
      title: '供应商简称',
      width: 150,
      filters: [{ data: '' }],
      filterMethod({
        option,
        row,
        column,
      }: {
        column: any;
        option: any;
        row: any;
      }) {
        if (option.data) {
          return `${row[column.field]}`.includes(option.data);
        }
        return true;
      },
      filterRender: {
        name: 'FilterInput',
      },
    },
    {
      field: 'creditCode',
      title: '统一社会信用代码',
      width: 150,
      filters: [{ data: '' }],
      filterMethod({
        option,
        row,
        column,
      }: {
        column: any;
        option: any;
        row: any;
      }) {
        if (option.data) {
          return `${row[column.field]}`.includes(option.data);
        }
        return true;
      },
      filterRender: {
        name: 'FilterInput',
      },
    },
    {
      field: 'registeredProvince',
      title: '注册所在省',
      width: 150,
    },
    {
      field: 'registeredCity',
      title: '注册所在市',
      width: 150,
    },
    {
      field: 'registeredCounty',
      title: '注册所在区县',
      width: 150,
    },
    {
      field: 'registeredAddress',
      title: '注册地址',
      width: 150,
    },
    {
      field: 'mainBusiness',
      title: '主营业务',
      width: 150,
      filters: [{ data: '' }],
      filterMethod({
        option,
        row,
        column,
      }: {
        column: any;
        option: any;
        row: any;
      }) {
        if (option.data) {
          return `${row[column.field]}`.includes(option.data);
        }
        return true;
      },
      filterRender: {
        name: 'FilterInput',
      },
    },
    {
      field: 'classify',
      title: '供应商分类',
      width: 150,
      slots: {
        default: 'classify_slot',
      },
      filters: [
        { label: '服务外包', value: 'SERVICE_OUTSOURCING' },
        { label: '机械租赁', value: 'MECHANICAL_LEASING' },
        { label: '劳务分包', value: 'LABOR_SUBCONTRACTING' },
        { label: '物资采购', value: 'MATERIAL_PURCHASING' },
        { label: '专业分包', value: 'PROFESSIONAL_SUBCONTRACTING' },
      ],
      filterMethod({ option, row, column }: any) {
        return `${row[column.field]}`.includes(option.value);
      },
    },
    {
      field: 'jobContent',
      title: '供应工作内容',
      width: 150,
      filters: [{ data: '' }],
      filterMethod({
        option,
        row,
        column,
      }: {
        column: any;
        option: any;
        row: any;
      }) {
        if (option.data) {
          return `${row[column.field]}`.includes(option.data);
        }
        return true;
      },
      filterRender: {
        name: 'FilterInput',
      },
    },
    {
      field: 'region',
      title: '供应区域',
      width: 150,
    },
    {
      field: 'unitType',
      title: '单位类型',
      width: 150,
    },
    {
      field: 'taxpayerQualification',
      title: '纳税人资质',
      width: 150,
      slots: {
        default: 'taxpayerQualification_slot',
      },
    },
    {
      field: 'registeredCapital',
      title: '注册资金（万元）',
      width: 150,
    },
    {
      field: 'establishAt',
      title: '成立时间',
      width: 150,
    },
    {
      field: 'contactBy',
      title: '联系人',
      width: 150,
    },
    {
      field: 'contactPhone',
      title: '联系电话',
      width: 150,
    },
    {
      field: 'LegalBy',
      title: '法定代表人/单位负责人',
      width: 150,
    },
    {
      field: 'relationEnterprise',
      title: '关联企业',
      width: 150,
    },
    {
      field: 'remark',
      title: '备注',
      width: 150,
    },
    {
      field: 'creator',
      title: '提交人',
      width: 150,
    },
    {
      field: 'publishAt',
      title: '发布时间',
      width: 150,
    },
    {
      field: 'createAt',
      title: '提交时间',
      width: 150,
    },
  ],
  proxyConfig: {
    response: {
      result: 'list', // 指定后端返回中列表字段的名称
    },
    ajax: {
      query: async () => {
        return await ListSupplier();
      },
    },
  },
});
function drawerRefresh() {
  supplierOptionsRef.value.commitProxy('query');
  rowId.value = '';
}

function jumpDrawer(row: any) {
  showDrawer.value = true;
  rowId.value = row.id;
}
// 供应商分类
const supplierLabel = computed(() => {
  return function (val: any) {
    const labels = val.map((item) => {
      return supplierCategory.find((category) => category.value === item)
        ?.label;
    });
    return labels.join(',');
  };
});
// 纳税人类型
const taxpayerLabel = computed(() => {
  return function (val) {
    return taxpayerType.find((item) => item.value === val)?.label;
  };
});
// 导入
function handleDropDownItem(command: string) {
  if (command === 'download') {
    downloadLocalFile('/file/供应商名录.xlsx', '供应商名录模版.xlsx');
  }
}
</script>

<style scoped lang="scss">
:deep(.el-drawer) {
  .el-drawer__header {
    margin-bottom: 0;
  }
}
</style>
